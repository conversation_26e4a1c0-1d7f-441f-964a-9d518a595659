Article

Knowledge-guided deep learning reveals environment-specific microwave
precursors to major earthquakes

Pan <PERSON>ong \textsuperscript{1}, <PERSON> \textsuperscript{2}, <PERSON><PERSON> \textsuperscript{3}, <PERSON> \textsuperscript{4,5},
<PERSON> \textsuperscript{6} and <PERSON><PERSON> \textsuperscript{7}

\textsuperscript{1} Institute of Earthquake Forecasting, China
Earthquake Administration, Beijing, China

\textsuperscript{2} College of Computing and Data Science, Nanyang
Technological University, Singapore, Singapore

\textsuperscript{3} School of Computing and Mathematical Sciences,
University of Leicester, Leicester, United Kingdom

\textsuperscript{4} Department of Physics, University of Trento, Trento,
Italy

\textsuperscript{5} National Institute for Nuclear Physics, The Trento
Institute for Fundamental Physics and Applications, Trento, Italy

\textsuperscript{6} Istituto Nazionale di Geofisica e Vulcanologia,
Rome, Italy

\textsuperscript{7} National Space Science Center, Chinese Academy of
Sciences, Beijing, China

\textbf{Abstract:} 

1. Introduction

Earthquakes remain one of the most devastating natural hazards, causing
significant loss of life and economic damage worldwide due to their
often unpredictable nature {[}1{]}. The scientific community
continuously strives for reliable earthquake prediction or, more
realistically, the identification of credible short-term precursors that
might signal an increased probability of an impending seismic event
{[}1--3{]}. Recognizing such precursory phenomena is crucial for
developing timely hazard mitigation strategies and potentially saving
lives {[}4{]}.

Among various approaches explored for detecting earthquake precursors
(e.g., geochemical anomalies, electromagnetic field variations,
seismicity pattern changes), and while multi-parameter studies combining
various data types offer valuable corroborating evidence, Earth
observation techniques offer unique advantages like broad spatial
coverage and continuous monitoring capabilities {[}4,5{]}. While thermal
infrared (TIR) anomalies have been widely investigated {[}1{]},
microwave remote sensing, specifically Microwave Brightness Temperature
(MBT) measurements, provides a complementary and potentially robust
alternative {[}5{]}. Passive microwave radiometry offers all-weather
monitoring capabilities, effectively penetrating clouds, fog, and even
some surface cover (like dry sand or sparse vegetation) which often
obscure TIR signals {[}6--8{]}. However, MBT signals are also known to
be strongly influenced by numerous non-seismic environmental factors,
posing significant challenges for precursor identification. MBT is
inherently sensitive to the surface and near-surface dielectric
properties and physical temperature {[}6,9{]}, which are influenced by
parameters like soil moisture, surface roughness, and potentially, the
stress-induced changes within the Earth\textquotesingle s crust during
the earthquake preparation phase {[}10--14{]}. Laboratory experiments
have even detected microwave emissions during rock deformation and
fracture, providing a physical basis for satellite detection concepts
{[}14--16{]}. Instruments like the Advanced Microwave Scanning
Radiometer for Earth Observing System (AMSR-E), its successor AMSR-2,
and the Microwave Radiation Imager (MWRI) have provided valuable
long-term MBT data across multiple frequencies and polarizations
{[}8,13,17,17--19{]}, forming the basis for numerous seismogenic anomaly
studies {[}5,13,19--24{]}.

Despite its potential, reliably identifying seismic precursors using MBT
data poses significant challenges {[}5,24{]}. Precursory signals, if
they exist, are typically weak (often only a few Kelvin) {[}18,21,25{]}
and deeply embedded within complex background variations stemming from
natural but non-seismic (NbNS) factors, which far exceed the signal
magnitude {[}13,18,21,24,25{]}. Furthermore, a major hurdle is the
inconsistent correlation between observed MBT fluctuations and actual
seismic events. Sometimes, significant earthquakes occur without clear
preceding MBT anomalies, while other times, apparent anomalies arise
without subsequent seismicity, significantly hindering reliable
precursor identification. MBT observations are heavily influenced by a
multitude of these NbNS factors, creating significant noise that can
mask or mimic seismic signals {[}5,18,22{]}. Atmospheric conditions,
particularly water vapor content and cloud liquid water, significantly
affect higher frequencies (\textgreater20 GHz) {[}5,6{]}. Land surface
characteristics dominate the signal, including highly variable soil
moisture (a primary driver of dielectric changes, especially at lower
frequencies) {[}5,6,9,26{]}, dynamic vegetation cover (influencing
scattering and absorption) {[}5,6,18,22{]}, seasonal and diurnal surface
temperature changes {[}13,17{]}, snow cover and freeze-thaw cycles
{[}17,18{]}, and surface roughness {[}6,10,17,27{]}. In marine
environments, factors like sea state (wave height, roughness), salinity,
and sea foam also play crucial roles {[}6,17,28{]}.

Numerous studies have reported potential MBT anomalies preceding
significant earthquakes globally, including events in Wenchuan (2008)
{[}13,21,27{]}, Nepal (2015) {[}19{]}, Madoi (2021) {[}23{]}, Iran
(2017) {[}23{]}, the Zhangbei-Bohai fault zone {[}28{]}, and the broader
Tibetan Plateau {[}17,17,22{]}. These studies have employed various
methods, from statistical indices and visual interpretation {[}14,16{]}
to more sophisticated techniques like the Spatio-Temporally Weighted
Two-Step Method (STW-TSM) {[}13,19,21{]} and wavelet analysis {[}8{]},
often attempting to discriminate seismic signals from background noise.
Some studies also explored multi-parameter approaches, correlating MBT
with atmospheric gases like CO or CH4, potentially linked to fault
degassing from submarine or terrestrial sources {[}24,28{]}. However,
current research faces several critical limitations that hinder progress
towards reliable precursor identification {[}24{]}. First, there is a
lack of universally consistent anomaly analysis methodologies, leading
to varying results and difficulty in establishing generalizable
patterns. Second, many analyses fail to adequately and explicitly remove
the confounding influence of the diverse NbNS factors {[}5,13,18,22{]} ,
making it difficult to isolate potential seismic signals when relying
solely on MBT data. The strong spatial heterogeneity of these factors
across different landscapes (e.g., oceans, forests, deserts, varied
topography like the Qinghai-Tibet Plateau) is often under addressed
{[}18,22,23{]}. Methods or thresholds optimized for one environment may
perform poorly in another {[}22,23{]}. Third, the optimal microwave
channels (frequency and polarization) most sensitive to seismic
precursors likely vary depending on the local geological setting,
coversphere conditions, and the specific NbNS factors dominant in that
region {[}18,22,24{]}. Yet, channel selection is often empirical or
lacks rigorous justification {[}24{]}. Fourth, many existing methods
struggle to reliably resolve the inherent signal ambiguity where
observed patterns do not consistently map to seismic occurrences across
diverse conditions. Therefore, a critical need exists for advanced
methodologies that explicitly address environmental heterogeneity,
systematically determine optimal channels based on insensitivity to
local NbNS factors {[}22{]}, and leverage multi-channel information more
effectively to improve the reliability of seismic MBT anomaly detection
{[}5,24{]}.

To address these challenges, this paper proposes a comprehensive
methodological framework designed for enhanced detection of potential
MBT seismic precursors through environment-specific analysis and deep
learning. Our approach integrates five key components: (1) Surface Type
Classification: We first categorize the global land/ocean surface into
five distinct environmental zones based on quantifiable criteria
(vegetation cover, soil moisture) to enable tailored analysis reflecting
distinct microwave radiative properties {[}9,18,23,26{]}. (2) Structured
Data Sampling: We identify potential earthquake-related MBT data based
on temporal proximity and spatial relationship (Dobrovolsky radius
{[}2{]}) to major (M≥7.0) earthquakes, creating balanced datasets for
comparison against non-seismic background periods. (3) Clustering
Analysis: Continuous MBT values are discretized using K-means
clustering. (4) Association Rule Mining: We employ the Apriori algorithm
with a support-difference criterion to automatically discover
zone-specific frequency-polarization combinations (itemsets) that
exhibit significantly higher prevalence before major earthquakes, thus
identifying potentially optimal channels {[}22{]}. (5) Novel WE-FTT
Model: We introduce the Weight-Enhanced Feature-based Transformer
(WE-FTT), a deep learning model specifically adapted for seismic anomaly
detection in MBT data. Its core innovation lies in explicitly
integrating the domain knowledge gained from association rule
mining---representing the learned importance of specific channels for
precursor identification within each environmental zone {[}22,23{]}.
This integration is specifically designed to guide the model towards the
most seismically informative features, thereby enhancing its ability to
discern true precursory patterns and effectively learn the decision
boundary between seismic and non-seismic states, even when input signals
appear noisy or ambiguous. The model leverages support values from
discovered association rules as importance weights for different
frequency-polarization channels, guiding the model\textquotesingle s
attention towards the most seismically informative features for each
specific environment. This knowledge-enhanced approach enhances the
model\textquotesingle s ability to detect subtle precursory patterns
potentially linked to physical mechanisms like P-hole activation
(leading to dielectric constant changes) {[}12,28--30{]} or
Lithosphere-Atmosphere-Ionosphere Coupling (LAIC) effects
{[}19,24,31{]}.

The primary objectives of this study are: (i) To identify and
characterize reliable MBT frequency-polarization signatures potentially
associated with major (M≥7.0) earthquakes across five distinct surface
environments using association rule mining. (ii) To develop and evaluate
the performance of the novel WE-FTT model in distinguishing between
potentially anomalous (pre-earthquake) and background (non-seismic) MBT
data segments, tailored to specific environmental zones. (iii) To
demonstrate the effectiveness of the proposed frequency itemset-based
weight optimization strategy and the overall framework through rigorous
comparative analysis with baseline models and comprehensive ablation
studies. Our results aim to contribute towards more robust and reliable
methods for identifying potential earthquake precursors from satellite
microwave data.

This paper is structured as follows: Section 2 describes the AMSR-2 MBT
dataset, environmental datasets, and the earthquake catalog used.
Section 3 details the complete methodological framework, encompassing
surface classification, data sampling, K-means discretization,
association rule mining for channel importance discovery, and the
architecture of the WE-FTT model, particularly its weight integration
mechanism. Section 4 presents the results, including the identified
association rules for each environmental zone, the classification
performance metrics of the WE-FTT model compared to baseline models, and
the findings from the ablation studies verifying the contribution of the
weight enhancement. Section 5 discusses the significance and
implications of these findings in the context of precursor research,
compares them with existing literature, acknowledges the
study\textquotesingle s limitations, and suggests avenues for future
work, including potential links to physical mechanisms. Finally, Section
6 concludes the study by summarizing the main contributions and
reinforcing the potential of the proposed environment-specific,
knowledge-guided deep learning approach for improving the reliability of
MBT-based seismic anomaly detection.

2. Data

The microwave brightness temperature (MBT) data used in this study are
derived from the Advanced Microwave Scanning Radiometer 2 (AMSR-2)
instrument, which continues the legacy of AMSR on ADEOS-II and AMSR-E on
Aqua. Since its launch in 2012, AMSR-2 has reliably measured global
microwave emissions{[}32{]}. Operating at six frequency bands ranging
from 7GHz to 89GHz, each with vertical and horizontal polarization,
AMSR-2 provides 12 channels (Ten channels, specifically 6.9, 10.65,
23.8, 36.5, and 89.0 GHz for both H and V polarizations, were utilized
in the final modeling stage, as detailed in Section 3.5). The spatial
resolution (IFOV) varies by frequency, from about 24 × 42 km to 3 × 5
km{[}33{]}. This study focuses on nighttime (descending-mode) AMSR-2 MBT
data to minimize interference from diurnal solar radiation and
anthropogenic activity, thus providing a more stable baseline for
anomaly detection {[}18{]}. We used a 0.25° grid chosen for consistency
with available auxiliary datasets (soil moisture, vegetation) and common
practice in global studies {[}9,23{]} cover January 2013 - August 2023,
providing a long-term dataset for analyzing variability in microwave
emissivity.

To more effectively identify microwave brightness temperature (MBT)
anomalies potentially associated with seismic events, this study
integrates multiple data sources and auxiliary information. First, we
incorporate daily soil moisture data from NASA GES DISC (0.25°
resolution){[}34{]} and vegetation coverage data from ERA5 (0.25°
resolution, originally hourly but averaged to daily) {[}7,35{]}. These
datasets help characterize the surface environment and distinguish
intrinsic land surface-driven MBT variations from those that might be
related to earthquakes. Additionally, to establish a robust foundation
for correlating MBT anomalies with seismic activity, we include a
comprehensive global earthquake catalog sourced from the U.S. Geological
Survey (USGS) website
(https://www.usgs.gov/programs/earthquake-hazards/earthquakes). This
dataset encompasses all earthquake events with magnitudes of 4.8 and
above, totaling 32,123 occurrences from January 1, 2013, to August 1,
2023. Although the catalog includes events down to M≥4.8, preliminary
analyses indicated a lack of consistent MBT anomalies associated with
smaller magnitudes in our global dataset. Therefore, to focus on
potentially clearer signals and facilitate robust methodological
development, this study restricts its primary analysis to major
earthquakes (M≥7.0), acknowledging that this limits direct applicability
to smaller, more frequent events. Each event record provides essential
information such as timing, location (latitude and longitude), and
magnitude. By combining MBT observations with environmental conditions
(soil moisture and vegetation) and a detailed earthquake catalog, this
study creates a multidimensional analytical framework aimed at isolating
subtle MBT anomalies and evaluating their potential linkage to seismic
processes {[}23,36{]}.

3. Methodology

This research employs a comprehensive methodological framework for
detecting seismic precursors using microwave brightness temperature
(MBT) data, as illustrated in Figure 1. The framework integrates five
key components: (1) surface type classification that categorizes the
study area into distinct zones based on environmental characteristics;
(2) structured data sampling that identifies earthquake-related and
non-seismic MBT data using temporal and spatial criteria; (3) clustering
analysis that transforms continuous MBT values into discrete clusters;
(4) association rule mining that discovers frequency-polarization
combinations with high predictive power for seismic events; and (5) a
novel Weight-Enhanced Feature-based Transformer (WE-FTT) model that
synthesizes these components by pre-computing importance weights from
support values and integrating them through parallel projection pathways
and element-wise multiplication with feature embeddings. This integrated
approach enables environment-specific anomaly detection by dynamically
adjusting the importance of different frequency channels based on their
relevance to seismic activity in each zone. The following sections
detail each component of this methodological framework.

3.1 Surface Type Classification

Given the high sensitivity of MBT signals to surface environmental
conditions (NbNS factors), a crucial first step towards isolating
potential seismic signals is to analyze data within more homogeneous
environmental contexts. Therefore, we classified the study area into
five distinct zones based on vegetation coverage, soil moisture, and
surface characteristics. The classification criteria are summarized in
Table 1. For implementation, we processed daily vegetation coverage data
from ERA5 and soil moisture data from NASA GES DISC to match our 0.25°
analysis grid. After applying the land-sea mask to identify Zone A, we
classified terrestrial regions into Zones B-E using the threshold
criteria. The earthquake catalog was then partitioned according to these
zones based on event epicenter locations for subsequent zone-specific
analysis. This classification enables us to account for varying
environmental conditions affecting MBT measurements and develop targeted
analytical approaches for each zone\textquotesingle s unique
characteristics.

\includegraphics[width=5.01475in,height=5.92312in]{./media/media/image1.png}

Figure . Methodological framework for MBT-based seismic anomaly
detection. The workflow illustrates the integration of surface type
classification, earthquake-related data sampling, clustering analysis,
association rule mining, and the Weight-Enhanced Feature-based
Transformer (WE-FTT) model. The WE-FTT incorporates mining-derived
support values as pre-computed weights that are projected and multiplied
with feature embeddings prior to attention computation, enabling
enhanced seismic precursor detection across diverse environmental zones.

Table . Classification criteria for different surface types

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.0772}}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.1801}}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.2403}}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.1601}}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.0155}}
  >{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.3268}}@{}}
\toprule\noalign{}
\begin{minipage}[b]{\linewidth}\raggedright
Zone
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Surface Type
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Vegetation Coverage
\end{minipage} &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.1756} + 2\tabcolsep}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Soil Moisture
\end{minipage}} & \begin{minipage}[b]{\linewidth}\raggedright
Characteristics
\end{minipage} \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
A & Marine Zone & N/A & N/A &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.3423} + 2\tabcolsep}@{}}{%
Oceanic regions} \\
B & Humid Forest & \textgreater0.5 & \textgreater6 &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.3423} + 2\tabcolsep}@{}}{%
Tropical rainforests, dense vegetation with moist soil} \\
C & Dry Forest & \textgreater0.5 & ≤6 &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.3423} + 2\tabcolsep}@{}}{%
Subtropical savannas, significant vegetation with dry soil} \\
D & Wetland & ≤0.5 & \textgreater6 &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.3423} + 2\tabcolsep}@{}}{%
Wetlands and marshes} \\
E & Arid Land & ≤0.5 & ≤6 &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 10\tabcolsep) * \real{0.3423} + 2\tabcolsep}@{}}{%
Deserts and arid areas} \\
\end{longtable}

3.2 Identification of Earthquake-Related MBT Data

To identify MBT data associated with seismic events across different
surface types, we established temporal and spatial search criteria for
each zone (A-E), as shown in Figure 2. The figure illustrates the global
distribution of major earthquakes (M≥7.0) across different surface
zones, from marine areas to various terrestrial environments. For
temporal criteria, we examined MBT data within a window spanning from 20
days before to 10 days after each earthquake event (similar temporal
windows are explored in, e.g., {[}1,3,13{]}, selected to encompass
typical durations reported for potential short-term precursors and
immediate post-seismic effects), aiming to capture both potential
precursory signals and post-seismic effects. The spatial extent was
determined using the Dobrovolsky radius {[}2{]} (R =
10\textsuperscript{0.43M} km, where M is earthquake magnitude), a
standard, albeit potentially simplified, empirical relationship commonly
used to estimate the spatial extent of earthquake preparation zones,
which defines the theoretical preparation zone for each earthquake. We
focused on significant seismic events with magnitudes greater than 7.0,
as these events are more likely to produce detectable thermal anomalies.
For marine zones (Zone A), only shallow earthquakes with focal depths
less than 70 km were considered, as deeper events are less likely to
influence sea surface thermal emissions. MBT measurements were flagged
if they fell within both the temporal and spatial windows of any
qualifying earthquake event, with this process implemented through
parallel computing to handle the large dataset efficiently. This
identification process was performed independently for each surface
classification to maintain the distinction between different
environmental conditions.

3.3 Selection of Non-seismic MBT Data

To establish a balanced dataset for comparative analysis, we performed
random sampling of non-seismic MBT data for each surface type using a
systematic sampling approach. For each zone, we selected an equal number
of non-seismic samples as the corresponding earthquake-related data
identified in the previous step. For instance, in Zone A (Marine Zone),
where 375,043,248 MBT measurements were identified as
earthquake-related, we randomly selected an equal number of non-seismic
measurements from the same zone. This process was repeated for each
surface type (Zones B-E), resulting in ten distinct datasets: five
containing earthquake-related MBT data and five containing randomly
selected non-seismic data, with each pair corresponding to one of the
surface types. This balanced sampling approach helps minimize potential
bias in subsequent statistical analyses while maintaining the
environmental characteristics specific to each surface type.

\includegraphics[width=6.14167in,height=3.67639in]{./media/media/image2.png}

Figure . Global Distribution of Major Earthquakes (M≥7.0) Across
Different Surface Type Zones

3.4 Clustering Analysis and Association Rule Mining

To effectively analyze the MBT data patterns, we implemented a two-stage
data mining approach combining clustering analysis and association rule
mining. For clustering analysis, after comparing hierarchical
clustering, DBSCAN, and K-means algorithms, we selected the K-means
algorithm for its computational efficiency with large-scale datasets
{[}37{]}. The optimal number of clusters was determined using the Elbow
Method by calculating the Sum of Squared Errors (SSE) for different K
values {[}38{]}. The clustering quality was validated using the
Silhouette Coefficient, with values closer to 1 indicating better
clustering results {[}39{]}.

To address the challenge of signal ambiguity, we sought to identify
consistent, albeit potentially subtle, MBT patterns that reliably
precede earthquakes within specific environmental contexts. Association
rule mining provides a suitable framework for this task. For association
rule mining, we employed the Apriori algorithm to discover frequent
patterns in MBT data{[}40{]}. The algorithm identifies frequent itemsets
by leveraging the principle that subsets of frequent itemsets must also
be frequent {[}41{]}. We first transformed the continuous MBT values
from 10 AMSR-2 channels (specifically, 6.9H, 6.9V, 10.65H, 10.65V,
23.8H, 23.8V, 36.5H, 36.5V, 89.0H, 89.0V) into discrete clusters using
the K-means results to meet the Apriori algorithm\textquotesingle s
input requirements. The mining process was conducted separately for
earthquake-related and non-seismic data within each surface type (Zones
A-E). The support-difference criterion specifically targets the
discovery of itemsets whose prevalence significantly increases before
earthquakes, providing the knowledge base needed to guide the model
through ambiguous signal presentations.

To quantify the significance of different frequency combinations, we
used an internal support-difference approach as our selection criterion,
rather than a conventional fixed support and confidence threshold, by
calculating the support difference between earthquake-related and
non-seismic datasets for each frequent itemset. The support differences
were then normalized to a 0-1 scale using min-max normalization to
eliminate dimensional differences between frequency combinations and
facilitate comparative analysis {[}37{]}. We retained only the itemsets
with positive support differences, representing the most
earthquake-sensitive frequency combinations. Finally, we calculated the
average support for each individual frequency channel across all
frequent itemsets, providing insights into channel sensitivity across
different surface types. These results formed the basis for assigning
appropriate weights to different frequency channels in subsequent
analyses.

3.5 Feature-based Transformer with Dynamic Weight Optimization

3.5.1 Weight-Enhanced Feature Processing

Based on the mining results from Section 3.4, we implemented a
preprocessing approach to feature weighting for the 10 columns of MBT
data. To comprehensively consider the contribution of different
frequencies and polarizations in earthquake prediction, we augmented
each MBT data column with a corresponding weight column, pre-computed by
adding the support values from frequent itemset mining to their initial
value of 1. These pre-computed weights are provided alongside features
as model inputs, creating a dual-input architecture that explicitly
preserves the domain knowledge derived from our data mining process.
This explicit injection of channel importance, derived from identifying
consistent pre-seismic patterns, guides the model to prioritize these
reliable features. This reduces its susceptibility to potentially noisy
or misleading signals in less informative channels, thereby improving
its ability to handle signal ambiguity. This weight-enhanced feature
processing approach is visually represented in the parallel processing
pathways on the left side of Figure 3, where features and their
corresponding weights are processed through independent projection
networks before being combined through element-wise multiplication.

3.5.2 Adaptive FT-Transformer Architecture

We selected the FT-Transformer {[}42{]} as the core architecture of our
deep learning model, which effectively encodes both discrete and
continuous features from structured data into vectors. This enables
Transformer-based feature extraction similar to text data processing
{[}43{]}. This choice leverages the model\textquotesingle s excellence
in handling time series data and long-range dependencies, while fully
utilizing its advantages in processing high-dimensional feature spaces.

To better handle tabular data, we implemented several critical
adaptations to the original FT-Transformer architecture. A significant
modification was our specialized approach to sequence representation.
Instead of introducing a separate CLS token as in standard transformer
architectures, we implemented a more streamlined approach where feature
embeddings are combined with their corresponding projected weight
embeddings through element-wise multiplication. The LayerNorm was
strategically positioned after the positional encoding, which we found
was optimal for preserving the structural characteristics of MBT data.

The forward propagation process in our adapted architecture follows a
carefully designed sequence of operations. The model begins by
processing the input data through parallel projection pathways,
transforming the 10 MBT features and their corresponding 10 weight
values into embedded representations of equal dimensionality. These
projections are then combined through element-wise multiplication, which
effectively scales each feature\textquotesingle s representation
according to its associated weight, creating a weighted feature space
that prioritizes the most seismically relevant channels.

The combined representation is enhanced with positional encoding and
normalized before being processed through multiple attention blocks,
each implementing our enhanced multi-headed attention mechanism combined
with residual connections. The network\textquotesingle s stability is
maintained through strategically placed layer normalization operations,
which help manage the flow of gradients and prevent training
instability.

In the final stages of processing, the model extracts the processed
feature representation, which by this point has accumulated a
comprehensive understanding of the relationships between different MBT
channels and their potential correlations with seismic activity. This
representation is then passed through a prediction head, which generates
the final classification output. These architectural modifications work
in concert to enhance the model\textquotesingle s ability to understand
and process the complex structural features present in tabular MBT data,
ultimately improving its seismic prediction capabilities.

The complete architecture of our Weight-Enhanced FT-Transformer is
illustrated in Figure 3. The diagram visualizes the parallel processing
pathways for both MBT features and their corresponding weight vectors,
highlighting how these pathways converge through element-wise
multiplication before a sequence dimension is added via the unsqueeze
operation. The figure details the critical components including
positional encoding, layered attention blocks with residual connections,
and the specialized fusion mechanism that enables effective integration
of weight information throughout the network depth. This architecture
provides a comprehensive framework for capturing the complex
interactions between different microwave frequency channels while
maintaining sensitivity to seismically relevant patterns.

\includegraphics[width=6.14167in,height=2.05347in]{./media/media/image3.png}

Figure . Architectural overview of the Weight-Enhanced Feature-based
Transformer (WE-FTT). The model processes MBT features and corresponding
weight vectors through parallel projection networks, followed by
element-wise multiplication to create weighted feature representations.
These representations undergo positional encoding and normalization
before entering a series of attention blocks with residual connections.
Key components include independent feature and weight projection
pathways, multi-head attention mechanisms that operate on pre-weighted
inputs, and a specialized fusion block for final classification.

3.5.3 Enhanced Multi-Head Attention Mechanism

In our FT-Transformer architecture, we implemented a significantly
enhanced multi-head attention mechanism specifically designed for MBT
data analysis. The mechanism begins with an initial transformation
phase, where a linear projection layer (qkv\_proj) simultaneously
processes the input tensors into three distinct representations: query
(Q), key (K), and value (V). This unified transformation ensures
computational efficiency while maintaining the rich relationships
between these different aspects of the input data. The resulting
representations are then systematically divided into multiple attention
heads, allowing the model to capture different aspects of the
relationships between MBT features at various frequency bands. As shown
in Figure 3, our multi-head attention mechanism achieves effective
integration of feature and weight information through parallel
processing of query (Q), key (K), and value (V) linear projections.

The attention score calculation process operates independently within
each attention head, implementing a sophisticated mathematical
framework. The core computation involves a matrix multiplication between
the query matrix Q and the transposed key matrix K, which is then scaled
by the square root of the head dimension to prevent gradient instability
in deeper layers. This scaled attention mechanism is further refined
through dropout regularization. Importantly, the
weights\textquotesingle{} influence is already integrated into the input
representation through the earlier element-wise multiplication of
feature and weight embeddings, rather than directly modulating the
attention scores themselves. This indirect approach to weight
integration allows for more stable gradient flow while maintaining the
relative importance of features. By operating on these pre-weighted
feature embeddings, the multi-head attention mechanism can more
effectively learn and focus on the complex inter-channel relationships
identified as crucial for distinguishing seismic precursors, even when
the overall signal pattern is atypical or obscured by noise.

Following the score calculation, the mechanism enters a critical
processing phase where the raw attention scores are transformed into
meaningful feature representations. The process begins by applying a
softmax function to convert the attention scores into probability
distributions, ensuring that the model\textquotesingle s focus is
appropriately normalized across all features. A dropout layer is then
employed for regularization, helping to prevent overfitting and improve
the model\textquotesingle s generalization capabilities. These processed
attention weights are subsequently multiplied with the value (V)
representations to produce the output for each attention head,
effectively capturing the weighted importance of different feature
combinations.

The final stage of our attention mechanism focuses on integrating the
diverse perspectives captured by each attention head. The outputs from
all heads are concatenated into a unified representation, preserving the
distinct patterns and relationships identified at different levels of
abstraction. This consolidated representation then passes through an
output projection layer (o\_proj), which transforms the concatenated
attention outputs into the final feature representation. This carefully
designed multi-stage process ensures that our attention mechanism can
effectively capture both local and global patterns in the MBT data while
maintaining sensitivity to seismically relevant feature interactions.

3.5.4 Model Evaluation and Optimization

To ensure optimal model performance, we implemented a comprehensive
evaluation and parameter optimization framework. For addressing the
class imbalance issue in seismic data prediction, we developed a Dynamic
Focal Loss function that combines focal loss with adaptive class
weighting. This loss function utilizes a gamma parameter (0.5-5.0) to
adjust the focus on hard-to-classify samples and implements a
momentum-based weight update mechanism (momentum: 0.5-0.99) to
adaptively adjust class weights based on the model\textquotesingle s
recent performance history.

The model\textquotesingle s effectiveness was evaluated using multiple
metrics, with Matthews Correlation Coefficient (MCC) {[}44{]} serving as
the primary optimization metric due to its robustness in handling
imbalanced multi-class problems. Additional metrics including accuracy,
weighted precision-recall, F1 score, and Cohen\textquotesingle s Kappa
{[}45{]} were also monitored to provide a comprehensive assessment of
model performance across different surface type zones.

For hyperparameter optimization, we employed Optuna {[}46{]} to
systematically explore the parameter space through 30 trials with early
stopping. Key parameters optimized included architectural components
(number of attention heads: 4-32, input embedding dimension: 64-512,
attention blocks: 2-8), and training parameters (learning rate: 1e-5 to
1e-2, weight decay: 1e-6 to 1e-3). The training process utilized a
distributed setup across multiple GPUs, with an 80/20 train-test split
and gradient clipping (0.5-5.0) to ensure stability. This systematic
optimization approach, combined with our enhanced model architecture and
weight optimization strategy, resulted in robust predictive performance
across different surface types and seismic conditions.

Additionally, we conducted comprehensive ablation studies to evaluate
the contribution of individual architectural components, with particular
focus on weight projection, attention mechanisms, and feature fusion
strategies.

3.6 Model Comparison

To evaluate the effectiveness of the proposed frequency itemset-based
weight optimization methodology, we conducted comprehensive comparative
experiments utilizing three distinct architectural approaches: our
Weight-Enhanced FT-Transformer (WE-FTT), TabNet{[}47{]}, and CatBoost
{[}48{]}. The experimental framework maintained consistent input
features across all models, comprising 10 MBT measurements (BT\_06\_H/V
through BT\_89\_H/V) and their corresponding weight vectors derived from
support values.

The integration of support-based weights manifests differently across
the three architectural paradigms, each employing distinct mechanisms
for incorporating weight information into their respective learning
processes. In the Weight-Enhanced FT-Transformer architecture, weights
influence the network through a parallel projection pathway. This
approach implements a sophisticated feature-weight integration scheme
where channel-specific weights derived from support values are projected
into the same embedding space as features, followed by element-wise
multiplication prior to attention computation. This pre-weighting of
features indirectly influences the attention mechanism by adjusting the
relative importance of different channels in the representation space.
The architecture further enhances this weight integration through
residual connections and layer normalization, maintaining the influence
of support-based weights throughout the network\textquotesingle s depth.

The TabNet architecture approaches weight integration through a
fundamentally different mechanism, employing a sequential feature
selection strategy. This model implements a dual-pathway approach to
weight utilization: first, through direct feature-level weighting in the
feature selection masks, and second, through sample-level importance
weighting in the loss function computation. The feature selection
process is guided by learnable masks that are influenced by the
support-based weights, allowing the model to adaptively focus on the
most relevant features at each decision step. This sequential processing
enables the model to capture complex feature interactions while
maintaining interpretability through explicit feature selection.

In contrast, the CatBoost architecture integrates weights through its
gradient boosting framework, incorporating them directly into the tree
construction process. The model utilizes weights in multiple aspects of
its learning procedure: in the selection of split points during tree
construction, in the calculation of leaf values, and in the bootstrap
sampling process for building individual trees. This comprehensive
weight integration strategy affects both the model\textquotesingle s
structure and its learning dynamics, with weights influencing both the
feature selection process and the final prediction computation.

Each architectural approach presents distinct advantages in handling
weighted features. The WE-FTT\textquotesingle s architecture excels in
capturing complex feature interactions through its unique parallel
projection and element-wise multiplication approach, which maintains the
relative importance of features throughout the network by establishing
weighted representations before attention computation.
TabNet\textquotesingle s sequential feature selection provides explicit
interpretability in feature utilization, particularly valuable in
analyzing seismic patterns. CatBoost\textquotesingle s tree-based
approach offers robust handling of weighted features through its
gradient boosting framework, particularly effective in capturing
non-linear relationships in the data. Compared to other architectural
approaches, our WE-FTT model, as illustrated in Figure 3, achieves
superior performance in seismic precursor detection through its
distinctive feature-weight parallel processing pathways and
multi-layered attention mechanisms, enabling more refined weight
modulation when analyzing microwave brightness temperature data.

To further validate our approach, we extended our comparative analysis
to include several classical machine learning models: XGBoost, LightGBM,
Multi-Layer Perceptron (MLP), and Random Forest. These models were
selected for their distinct approaches to feature weight integration.
The gradient boosting frameworks (XGBoost and LightGBM) incorporate
weight information through instance-based weighted gradient
calculations, with XGBoost directly scaling the gradients during tree
construction and LightGBM employing a histogram-based approach for
weight integration. The MLP serves as a deep learning baseline,
implementing weight integration through feature concatenation and fully
connected layers, while the Random Forest provides an ensemble
perspective through weighted bootstrap sampling and voting mechanisms.
While these classical models demonstrate robust performance in tabular
data analysis, their straightforward weight integration mechanisms
contrast with our Weight-Enhanced FT-Transformer\textquotesingle s
sophisticated attention-based approach. The gradient boosting models
excel in capturing non-linear relationships but lack dynamic feature
interaction capabilities, while both MLP and Random Forest show
limitations in adapting to dynamic weight adjustments during prediction.

4. Results

4.1. Analysis of Multi-frequency Microwave for Zone-Dependent
Pre-seismic Anomaly Detection

Preliminary testing indicated that smaller events lacked consistent MBT
anomalies, so we restricted final analyses to M≥7.0. The integrated
methodological framework presented in Figure 1 enabled comprehensive
analysis of AMSR-2 microwave brightness temperature data across five
distinct environmental zones has revealed characteristic pre-seismic
anomaly patterns through specific frequency-polarization combinations,
demonstrating significant detection capabilities for seismic precursor
signals. Through comprehensive frequent itemset mining analysis, we have
identified environment-specific anomaly signatures that achieve perfect
or near-perfect support values, indicating robust seismic precursor
detection capabilities across diverse landscape conditions (Figure 4).

\includegraphics[width=5.74839in,height=3.19231in]{./media/media/image4.png}

\textbf{Figure 4}. Frequency-polarization combinations for seismic
anomaly detection across environmental zones. Primary combinations
(blue) and secondary combinations (green) highlight varying
sensitivities, with each bar labeled with the corresponding
frequency-polarization combination (H: Horizontal, V: Vertical) to
indicate the most effective configurations in each zone.

In marine environments (Zone A), distinct pre-seismic anomalies were
most effectively captured by the synergistic combination of 89GHz
H-polarization (219.34-252.97K) and 36.5GHz V-polarization
(122.72-247.56K), achieving near-perfect detection reliability (support:
1.0000). This primary anomaly signature was complemented by a secondary
detection combination of 23.8GHz H-polarization (177.20-234.61K) and
36.5GHz H-polarization (109.31-209.29K) with near-perfect reliability
(support: 0.9923). The identified temperature range overlap (177-209K)
establishes a critical detection window for marine seismic precursor
identification.

Humid forest zones (Zone B) exhibited unique pre-seismic anomaly
signatures, with optimal detection achieved through the combination of
89GHz V-polarization (98.03-295.37K) and 36.5GHz V-polarization
(139.57-297.18K) at perfect support. The detection reliability showed
systematic degradation with additional frequency components:
three-frequency combinations including 10.65GHz H-polarization
(168.79-385.17K) maintained 0.5154 detection reliability, while 6.9GHz
H-polarization (163.66-388.18K) combinations achieved 0.5151. This
precise degradation pattern (Δsupport ≈ 0.0003) quantifies vegetation
effects on anomaly detection sensitivity.

Dry forest environments (Zone C) revealed distinct seismic precursor
signatures through the combination of 36.5GHz H-polarization
(121.40-207.09K) and 10.65GHz V-polarization (159.63-220.37K), achieving
near-perfect detection reliability. The exceptional stability of 36.5GHz
H-polarization temperature ranges (σ \textless{} 0.1K) indicates
consistent anomaly detection capability. Secondary detection
combinations incorporating 6.9GHz V-polarization (152.28-215.62K)
achieved 0.9976 reliability, with anomaly signatures concentrated in the
159-207K range.

Wetland zones (Zone D) demonstrated complex pre-seismic anomaly
patterns, achieving optimal detection through various combinations
incorporating 6.9GHz H-polarization (73.34-166.35K). The anomaly
signatures showed systematic stratification: low-frequency
H-polarization displayed broad detection ranges (Δt ≈ 93K),
mid-frequency V-polarization exhibited intermediate detection windows
(Δt ≈ 164K for 23.8GHz), and high-frequency channels showed
complementary anomaly detection characteristics (Δt ≈ 214K for
V-polarization).

In arid zones (Zone E), seismic precursor detection was optimized
through the combination of 23.8GHz H-polarization (189.04-240.88K) and
36.5GHz V-polarization (103.18-246.57K), achieving perfect reliability
while individual channels showed significantly lower detection
capabilities (0.0693 for 23.8GHz V). Low-frequency channels exhibited
broad anomaly detection ranges (Δt ≈ 154K for 6.9GHz H) despite limited
reliability (≤0.005), indicating complex subsurface precursor
mechanisms.

4.2. Terrain-Specific Seismic Sensitivity Assessment Using Normalized
Mean Support Values

To evaluate AMSR-2\textquotesingle s multi-frequency microwave
sensitivity to seismic anomalies, we conducted a comprehensive analysis
of mean support values across diverse terrain types. The study
categorized surface types into five zones based on vegetation coverage
and soil moisture content: marine areas (Zone A), high vegetation-high
soil moisture areas (Zone B), high vegetation-low soil moisture areas
(Zone C), low vegetation-high soil moisture areas (Zone D), and low
vegetation-low soil moisture areas (Zone E). We analyzed horizontal and
vertical polarization channels at 6.9 GHz, 10.65 GHz, 23.8 GHz, 36.5
GHz, and 89.0 GHz frequencies, calculating mean support values across
all frequent itemsets to assess seismic anomaly detection capabilities.

\includegraphics[width=6.14167in,height=5.40972in]{./media/media/image5.png}

\textbf{Figure 5}. Illustration of the polarization dependence of
microwave response characteristics across different terrain zones. The
mean support values were evaluated as a function of frequency for (a)
horizontal and (b) vertical polarization. The optimal microwave
brightness temperature (MBT) ranges and their associated support values
were determined for (c) horizontal and (d) vertical polarization
configurations.

Analysis of mean support values revealed distinctive patterns across
frequency bands and polarizations. At 6.9 GHz, horizontal polarization
demonstrated varying effectiveness across terrain types, with highest
support values in Zone D (0.9348, MBT: 166.36-655.35K), followed by Zone
C (0.5716, MBT: 73.61-145.63K). Vertical polarization at this frequency
showed similar patterns but with generally higher MBT ranges,
particularly in Zone D (0.9219, MBT: 214.9-655.35K). Zone B showed
moderate support values (0.2688, MBT: 163.66-388.18K), while Zone A
exhibited minimal sensitivity (0.0074, MBT: 41.88-168.06K).

The 10.65 GHz band exhibited enhanced detection capability, especially
in wetland areas (Zone D), where horizontal polarization achieved a
support value of 0.9306 (MBT: 171.55-655.35K) and vertical polarization
reached 0.9229 (MBT: 219.56-655.35K). Notably, dry forest regions (Zone
C) showed distinct polarization preferences, with vertical polarization
achieving 0.6220 (MBT: 159.63-220.37K). Zones A and B maintained
relatively low support values, with Zone B showing slightly better
performance (0.2689, MBT: 168.79-385.17K).

Higher frequency bands demonstrated more pronounced terrain-specific
responses. The 23.8 GHz horizontal polarization showed exceptional
performance in Zone D (0.9044, MBT: 201.73-655.35K), while maintaining
moderate effectiveness in Zone E (0.5075, MBT: 189.04-240.88K). Zone B
showed improved response at this frequency, reaching 0.3016 (MBT:
104.42-182.47K). The 36.5 GHz band achieved the highest overall support
values, particularly in Zone D where horizontal polarization reached
0.9713 (MBT: 209.48-655.35K) and vertical polarization achieved 0.8738
(MBT: 139.48-301.56K). The 89.0 GHz band showed unique capabilities in
arid regions (Zone E), with horizontal polarization achieving 0.7380
(MBT: 64.46-215.3K), while Zone A reached its maximum sensitivity at
this frequency (0.5078, MBT: 219.34-252.97K).

Multiple frequency combinations demonstrated enhanced detection
capabilities in specific terrains. In Zone D, the combination of 36.5
GHz and 23.8 GHz horizontal polarization channels provided optimal
detection with support values consistently above 0.90. Zone C showed
best response to combined 36.5 GHz horizontal polarization (0.7028, MBT:
121.4-207.09K) and 10.65 GHz vertical polarization (0.6219, MBT:
152.28-215.62K) observations. Zone E exhibited optimal sensitivity with
89.0 GHz horizontal polarization (0.7380, MBT: 64.46-215.3K) paired with
23.8 GHz vertical polarization (0.5224, MBT: 232.9-261.39K). Zones A and
B showed limited improvement with frequency combinations, suggesting
fundamental physical limitations in these environments.

\textbf{Table 2.} Segmented MBT ranges and corresponding mean support
values for dual-polarization microwave observations across distinct
terrain zones, the "-" symbol indicate in some cells not applicable.

\begin{longtable}[]{@{}
  >{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.0969}}
  >{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1808}}
  >{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.0020}}
  >{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1972}}
  >{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1692}}
  >{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1696}}
  >{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1842}}@{}}
\toprule\noalign{}
\multirow{2}{=}{\begin{minipage}[b]{\linewidth}\raggedright
Freq.
\end{minipage}} &
\multicolumn{6}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.9031} + 10\tabcolsep}@{}}{%
\begin{minipage}[b]{\linewidth}\raggedright
\textbf{Mean Support Value (MBT segmentation range)}
\end{minipage}} \\
&
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1828} + 2\tabcolsep}}{%
\begin{minipage}[b]{\linewidth}\raggedright
Zone A
\end{minipage}} & \begin{minipage}[b]{\linewidth}\raggedright
Zone B
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Zone C
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Zone D
\end{minipage} & \begin{minipage}[b]{\linewidth}\raggedright
Zone E
\end{minipage} \\
\midrule\noalign{}
\endhead
\bottomrule\noalign{}
\endlastfoot
\multirow{2}{=}{6.9 H} & 0.0074(41.8-168.1) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.2688(163.7-388.2)} & 0.5716(73.6-145.6) & 0.9348(166.4-655.4) &
0.4038(168.9-655.4) \\
& - &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.2201(73.4-163.7)} & 0.4408(226.8-382.8) & 0.8015(73.3-166.4) &
0.0042(14.3-168.9) \\
\multirow{2}{=}{6.9 V} & 0.0076(57.1-219.2) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.3095(252.2-388.1)} & 0.6219(152.3-215.6) & 0.9219(214.9-655.4) &
0.4038(218.6-655.4) \\
& - &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.2193(138.1-205.0)} & 0.4743(215.6-384.4) & 0.7759(139.7-214.9) &
0.0043(47.4-218.6) \\
\multirow{2}{=}{10.65 H} & 0.0075(68.9-173.7) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.2689(168.8-385.2)} & 0.5578(79.0-130.8) & 0.9306(171.6-655.4) &
0.4038(173.4-655.4) \\
& - &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.2202(78.9-168.8)} & - & 0.8014(78.9-171.5) & 0.0041(79.1-173.4) \\
\multirow{2}{=}{10.65 V} & 0.0075(121.3-223.9) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.2198(146.2-212.0)} & 0.6220(159.6-220.4) & 0.9229(219.6-655.4) &
0.4038(222.8-655.4) \\
& - &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
-} & 0.4748(220.4-385.4) & 0.7690(148.0-219.6) & 0.0042(146.6-222.8) \\
\multirow{3}{=}{23.8 H} & 0.5124(177.2-234.6) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.3016(104.4-182.5)} & 0.4676(233.8-292.5) & 0.9044(201.7-655.4) &
0.5075(189.0-240.9) \\
& 0.0132(103.3-177.2) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.2506(237.0-292.1)} & 0.2179(180.3-233.8) & - & 0.3366(104.8-189.0) \\
& - &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.1465(182.5-237.0)} & - & - & - \\
\multirow{2}{=}{23.8 V} & 0.0183(241.0-655.3) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.3029(157.2-256.0)} & 0.5043(257.4-302.6) & 0.8738(201.7-655.4) &
0.5224(232.9-261.4) \\
& 0.0069(162.9-241.0) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.1549(256.0-298.3)} & 0.0497(227.9-257.4) & - & 0.0426(168.1-232.9) \\
\multirow{2}{=}{36.5 H} & 0.0227(109.3-209.3) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.1745(121.7-185.1)} & 0.7028(121.4-207.1) & 0.9713(209.5-655.4) &
0.4052(210.9-655.4) \\
& - &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
-} & 0.5494(207.1-290.9) & 0.7824(111.5-209.5) & 0.0004(102.5-210.9) \\
\multirow{2}{=}{36.5 V} & 0.0228(122.7-247.6) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.3054(139.6-297.2)} & 0.5412(148.4-244.3) & 0.8738(139.5-301.6) &
0.4079(103.2-246.6) \\
& - &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
-} & 0.4229(244.3-300.6) & - & 0.0305(246.6-655.4) \\
\multirow{3}{=}{89.0 H} & 0.5078(219.3-253.0) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.3053(94.8-212.0)} & 0.5567(217.3-250.0) & 0.8738(80.6-295.9) &
0.7380(64.5-215.3) \\
& 0.0261(253.0-644.4) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.1638(246.1-292.9)} & 0.0340(250.0-293.4) & - & 0.0159(215.3-250.0) \\
& 0.0023(72.6-219.3) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.1335(212.1-246.1)} & - & - & - \\
\multirow{2}{=}{89.0 V} & 0.0179(270.7-655.4) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
0.3054(98.0-295.4)} & 0.5741(260.8-298.5) & 0.8738(82.8-298.8) &
0.2657(65.2-231.2) \\
& 0.0018(254.1-570.7) &
\multicolumn{2}{>{\raggedright\arraybackslash}p{(\linewidth - 12\tabcolsep) * \real{0.1992} + 2\tabcolsep}}{%
-} & 0.0001(229.1-260.8) & - & 0.0647(231.3-263.3) \\
\end{longtable}

4.3 Model Performance Evaluation

The proposed Weight-Enhanced Feature-based Transformer (WE-FTT)
architecture was systematically evaluated through comprehensive
classification analysis and comparative performance assessment against
established machine learning models. This section presents the detailed
results of these evaluations, demonstrating the effectiveness of the
frequency itemset-based weight optimization approach.

4.3.1 Classification Performance of the WE-FTT Model

The classification capabilities of the WE-FTT model were evaluated using
a confusion matrix analysis across all frequency-polarization
combinations, as shown in Figure 6. The model demonstrates remarkable
classification accuracy, achieving an average of 84.2\% correct
classifications across all microwave channels. This high accuracy is
particularly significant given the complex nature of the classification
task, which involves distinguishing between multiple
frequency-polarization combinations in the presence of environmental
variability and potential seismic anomalies.

\includegraphics[width=6.14167in,height=5.43611in]{./media/media/image6.png}

Figure . Brightness Temperature Polarization Classification Matrix from
the Weight-Enhanced FT-Transformer (WE-FTT) model across five AMSR-2
frequency bands (6.9, 10.65, 23.8, 36.5, and 89.0 GHz). Each cell
displays the sample count and row percentage, with color coding
indicating classification patterns: navy blue for correct
classifications (diagonal), light blue for H-V polarization confusion
within the same frequency, and coral red for cross-frequency confusions.
The high diagonal values (averaging 84.2\%) demonstrate the
model\textquotesingle s exceptional classification capability across all
frequency-polarization combinations, with particularly strong
performance in the 36.5 GHz and 89.0 GHz bands.

The confusion matrix reveals several important patterns in the
model\textquotesingle s classification behavior. First, the diagonal
elements (navy blue) show consistently high accuracy across all
channels, with particularly strong performance in the 36.5 GHz (84.2\%)
and 89.0 GHz (84.2\%) bands. Second, the light blue cells, representing
H-V polarization confusion within the same frequency, show minimal
misclassification (average 3.1\%), indicating the
model\textquotesingle s strong capability to distinguish between
polarization states. This is particularly important for seismic anomaly
detection, as polarization sensitivity provides critical information
about surface structural changes associated with seismic activity.

The coral-colored cells represent cross-frequency confusion, with color
intensity corresponding to the percentage of misclassifications.
Notably, the confusion predominantly occurs between adjacent frequency
bands (e.g., between 6.9 GHz and 10.65 GHz), which is physically
reasonable given the spectral proximity of these channels. The model
shows minimal confusion between spectrally distant channels (e.g.,
between 6.9 GHz and 89.0 GHz), with an average cross-band confusion of
only 1.9\%. This selective confusion pattern demonstrates the
model\textquotesingle s ability to capture the physical relationships
between frequency channels while maintaining discriminative power
between spectrally distinct measurements.

\includegraphics[width=5.85606in,height=4.40132in]{./media/media/image7.png}

Figure . Model performance comparison across six evaluation metrics. (a)
Matthews Correlation Coefficient (MCC), (b) F1 Score, (c) Accuracy, (d)
Precision, (e) Cohen\textquotesingle s Kappa, and (f) Recall. The
Weight-Enhanced FT-Transformer (red) consistently outperforms all
baseline models across all metrics, with RandomForest (navy blue)
showing second-best performance. The significant performance gap between
the proposed WE-FTT model and traditional approaches highlights the
effectiveness of the frequency itemset-based weight optimization
methodology.

4.3.2 Comparative Analysis of Model Performance

To rigorously evaluate the effectiveness of the proposed WE-FTT
approach, we conducted a comprehensive performance comparison against
five established machine learning models: RandomForest, LightGBM,
TabNet, CatBoost, and XGBoost. Each model was trained on identical
dataset splits and evaluated using six complementary performance
metrics: Matthews Correlation Coefficient (MCC), F1 Score, Accuracy,
Precision, Cohen\textquotesingle s Kappa, and Recall. Figure 7 presents
the detailed comparison across these metrics, revealing consistent
performance advantages for the WE-FTT architecture.

As shown in Figure 7, the proposed WE-FTT model achieved superior
performance across all evaluation metrics. The WE-FTT achieved an MCC of
0.84, F1 score of 0.82, accuracy of 0.84, precision of 0.80,
Cohen\textquotesingle s Kappa of 0.82, and recall of 0.84. These results
represent substantial improvements over the next best model
(RandomForest), which achieved an MCC of 0.74, F1 score of 0.70,
accuracy of 0.72, precision of 0.71, Cohen\textquotesingle s Kappa of
0.68, and recall of 0.72. This performance gap is especially significant
considering that MCC is particularly sensitive to imbalanced
classification scenarios characteristic of seismic anomaly detection
tasks. The consistent performance advantages across diverse metrics
demonstrate the WE-FTT\textquotesingle s robust generalization
capabilities across different evaluation criteria. The significantly
higher MCC score, in particular, highlights the WE-FTT\textquotesingle s
enhanced capability in handling the imbalanced nature and inherent
signal ambiguity of the seismic precursor detection task, where baseline
models likely struggle more to differentiate true positives from complex
background noise.

Statistical significance testing, as illustrated in Figure 8(a),
confirms the reliability of these performance differences. Bootstrap
resampling with 1,000 iterations generated 95\% confidence intervals for
the aggregated performance scores, revealing statistically significant
differences between the WE-FTT (0.8271) and RandomForest (0.7119,
p\textless0.01), as well as between subsequent model pairs in the
performance ranking. This statistical validation reinforces the
conclusion that the WE-FTT\textquotesingle s performance advantages are
systematic rather than artifacts of sampling variation.

The multi-dimensional performance profile visualization in Figure 8(b)
further illustrates the WE-FTT\textquotesingle s balanced excellence
across all metrics. While some baseline models showed uneven performance
profiles with strengths in certain metrics and weaknesses in others, the
WE-FTT maintained consistently high values across all dimensions,
creating a more uniformly expanded performance polygon. This balanced
profile indicates that the model achieves its high overall performance
without sacrificing specific aspects of classification quality.

Principal Component Analysis (PCA) of the performance metrics, shown in
Figure 8(c), provides additional insights into the relationships between
models and evaluation criteria. The first two principal components
capture 93.5\% of the total variance, with PC1 (78.2\%) primarily
representing overall performance and PC2 (15.3\%) capturing the
precision-recall trade-off dimension. The WE-FTT\textquotesingle s
position in the upper-right quadrant of this reduced feature space
demonstrates its dominance in both dimensions, while the clustering of
lower-performing models in the left region highlights the significant
performance gap between the proposed approach and traditional methods.

\includegraphics[width=5.37288in,height=4.80303in]{./media/media/image8.png}

Figure . Comprehensive model performance analysis. (a) Model ranking
based on aggregated performance scores with 95\% bootstrap confidence
intervals (n=1000). Statistical significance between adjacent models is
indicated (*p \textless{} 0.05, **p \textless{} 0.01). The WE-FTT
demonstrates statistically significant superior performance compared to
all other models. (b) Radar chart displaying performance profiles across
six evaluation metrics, where the FT\_Transformer (red) shows
consistently higher values compared to RandomForest (blue). (c)
Principal Component Analysis of model performance data, with the first
two components explaining 93.5\% of variance, revealing clear separation
between high-performing models (upper right) and lower-performing ones
(lower left).

To further examine the detailed classification patterns across different
models, Figure 9 presents the confusion matrices for all six evaluated
models. The WE-FTT model (Figure 9a) demonstrates substantially higher
diagonal values (correct classifications) compared to all other models,
with an overall accuracy of 84.2\%. In contrast, the next best
performer, RandomForest (Figure 9b), achieves only 71.9\% accuracy, with
notably increased cross-frequency confusion particularly between
adjacent frequency bands. The performance differential becomes even more
pronounced when comparing with the lower-performing models: LightGBM
(67.6\%), TabNet (63.0\%), CatBoost (58.1\%), and XGBoost (55.4\%).

A particularly noteworthy pattern emerges when examining the H-V
polarization confusion (light blue cells) across different models. The
WE-FTT demonstrates remarkably low in-band polarization confusion, while
traditional models show significantly higher confusion rates in this
category. This enhanced polarization discrimination capability can be
directly attributed to the weight optimization strategy, which
effectively leverages the distinctive information content of different
polarization channels based on their relevance to seismic anomaly
detection.

The confusion matrices also reveal that lower-performing models exhibit
increased misclassification between spectrally distant channels. This
pattern suggests that traditional models struggle to capture the complex
physical relationships between frequency channels, while the
WE-FTT\textquotesingle s attention mechanism effectively models these
interdependencies through its weight-enhanced feature interactions.

\includegraphics[width=5.4855in,height=8.10606in]{./media/media/image9.png}

Figure . Confusion matrices showing classification performance across
different models, with the percentage in parentheses indicating the
overall classification accuracy of each model: (a) WE-FTT (84.2\%), (b)
RandomForest (71.9\%), (c) LightGBM (67.6\%), (d) TabNet (63.0\%), (e)
Catboost (58.1\%), and (f) Xgboost (55.4\%). Each matrix displays
correct classifications (dark blue), H-V polarization confusion (light
blue), and cross-frequency confusion (coral red). The results
demonstrate that the WE-FTT model achieves significantly higher correct
classification rates compared to all baseline models, with RandomForest
showing the second-best performance.

4.3.3 Relationship to Frequency Itemset Mining Results

The superior performance of the WE-FTT model can be directly linked to
the frequency itemset mining results presented in Section 4.1. The
model\textquotesingle s pre-computed weight approach effectively
leverages the identified optimal frequency-polarization combinations for
each environmental zone. These weights, derived from the support values
identified in the itemset mining analysis, are processed through a
dedicated projection pathway before being combined with feature
embeddings through element-wise multiplication. For instance, in Zone D
(Wetland), the model assigns higher weights to the 36.5 GHz and 23.8 GHz
horizontal polarization channels, which were identified as having
exceptionally high support values (0.9713 and 0.9044 respectively) in
the itemset mining analysis. Similarly, for Zone C (Dry Forest), the
model emphasizes the 36.5 GHz horizontal polarization (support value:
0.7028) and 10.65 GHz vertical polarization (support value: 0.6220)
channels.

This superior ability to resolve ambiguous signals stems directly from
integrating the mined knowledge. By assigning pre-computed weights based
on rules identifying consistent pre-seismic signatures (e.g., high
support for 36.5 GHz H and 10.65 GHz V in Zone C), the WE-FTT
effectively learns the subtle, environment-specific decision boundary
between seismic and non-seismic states, enabling more reliable
classification even when individual channel readings might otherwise be
misleading. By focusing attention on the most informative
frequency-polarization combinations for each environmental context, the
WE-FTT achieves both improved classification accuracy and enhanced
physical interpretability of the learned patterns.

The significant performance improvements observed in the comparative
analysis thus validate the effectiveness of the frequency itemset-based
weight optimization methodology. By systematically mining the
relationships between frequency channels and their association with
seismic activity across different environmental conditions, and then
integrating this knowledge into the model\textquotesingle s attention
mechanism, the proposed approach successfully addresses the complex
challenges of microwave-based seismic anomaly detection.

4.4 Ablation Study

To gain deeper insights into the contribution of each architectural
component within our Weight-Enhanced FT-Transformer (WE-FTT) model, we
conducted a comprehensive ablation study. This analysis systematically
evaluated the impact of removing or replacing key components while
maintaining all other parameters constant. Our investigation focused on
six critical aspects of the architecture: input projection, attention
mechanism, feature fusion, loss function, position encoding, and
training strategy.

4.4.1 Experimental Design

For each architectural component, we designed specific ablation variants
to isolate their contribution:

1) Input Projection: We examined three variants - removing feature
projection, removing weight projection, and replacing multiplicative
fusion with additive fusion.

2) Attention Mechanism: We tested the impact of removing residual
connections and replacing multi-head attention with single-head
attention.

3) Feature Fusion: We evaluated the model without feature fusion and
with alternative fusion approaches.

4) Loss Function: We compared dynamic focal loss against standard
cross-entropy loss.

5) Position Encoding: We analyzed the contribution of positional
information by removing position encoding or replacing it with learnable
position embeddings.

6) Training Strategy: We assessed the impact of warmup periods and
learning rate decay on model performance.

Each experiment maintained identical training conditions, with fixed
hyperparameters to ensure fair comparisons. Performance was evaluated
using six metrics: Matthews Correlation Coefficient (MCC), F1 Score,
Accuracy, Precision, Cohen\textquotesingle s Kappa, and Recall.

Each experiment maintained identical training conditions with fixed
hyperparameters to ensure fair comparisons, and performance was
evaluated using six complementary metrics: Matthews Correlation
Coefficient (MCC), F1 Score, Accuracy, Precision,
Cohen\textquotesingle s Kappa, and Recall.

4.4.2 Component Contribution Analysis

Figure 10 presents the detailed performance metrics across all ablation
variants. The results reveal significant variations in model
effectiveness when key components are modified, with several important
findings:

\includegraphics[width=6.14167in,height=4.46944in]{./media/media/image10.png}

Figure . Ablation study results across six key architectural components:
(a) Input Projection shows performance with different feature and weight
projection approaches; (b) Attention Mechanism compares residual
connections and attention head configurations; (c) Feature Fusion
evaluates alternative fusion strategies; (d) Loss Function contrasts
cross-entropy and dynamic focal loss implementations; (e) Position
Encoding examines the impact of different positional information
strategies; and (f) Training Strategy analyzes warmup and learning rate
decay effects. Each bar represents performance scores across six
evaluation metrics: MCC, F1 Score, Accuracy, Precision, Kappa, and
Recall.

Input Projection demonstrates critical importance, with the removal of
weight projection causing the most substantial performance degradation
across all metrics (MCC: 0.42, F1: 0.45, Accuracy: 0.42), representing a
48.9\% reduction in MCC from the baseline. This confirms that the
dedicated weight projection pathway is fundamental to the
model\textquotesingle s ability to effectively transform pre-computed
support values into an embedding space where they can appropriately
scale feature representations before attention computation.

Attention Mechanism modifications reveal that removing residual
connections severely impacts performance (MCC: 0.65), while reducing to
single-head attention causes moderate degradation (MCC: 0.60). This
indicates that while multi-head attention contributes to model
effectiveness, the residual architecture plays an even more crucial role
in maintaining gradient flow and representation stability.

Feature Fusion experiments show that removing the feature fusion layer
reduces performance substantially (MCC: 0.63), while alternative fusion
methods achieve only slightly better results (MCC: 0.82). This suggests
that while some form of feature integration is necessary, the specific
implementation offers opportunities for architectural optimization.

Loss Function comparison demonstrates that the dynamic focal loss
outperforms standard cross-entropy (MCC: 0.65 vs. 0.63), confirming the
value of our adaptive class weighting mechanism in addressing data
imbalance challenges inherent in seismic anomaly detection.

Position Encoding analysis reveals relatively minor impacts when removed
(MCC: 0.79) or replaced with learnable embeddings (MCC: 0.83),
indicating that for tabular MBT data, positional information plays a
less critical role than in sequence modeling tasks.

Training Strategy evaluation shows that warmup periods significantly
contribute to model stability and performance (MCC: 0.77), while
learning rate decay provides moderate benefits (MCC: 0.77). This
highlights the importance of careful optimization scheduling in
achieving optimal convergence.

\includegraphics[width=6.14167in,height=4.14097in]{./media/media/image11.png}

Figure . Radar visualization of model performance metrics across
different ablation variants. Six distinct radar charts are shown, each
representing a specific evaluation metric perspective: (a) MCC (red),
(b) F1 Score (dark blue), (c) Accuracy (light blue), (d) Precision
(green), (e) Kappa (purple), and (f) Recall (orange). Each radar plot
illustrates how different architectural modifications affect model
performance across all evaluation dimensions, with the radial distance
from center representing the metric value (higher is better). The
visualization reveals consistent patterns of performance degradation,
particularly severe for the weight projection removal variants.

Figure 11 presents a multi-perspective radar visualization that enables
direct comparison of different ablation variants across all six
evaluation metrics simultaneously. Each of the six radar charts
represents a different metric perspective (MCC, F1 Score, Accuracy,
Precision, Kappa, and Recall), with each variant plotted as a point on
the radar. The distance from the center indicates performance value,
with greater distance representing better performance.

The radar visualization reveals critical patterns of metric imbalance
across different architectural variants. When examining the "No Weight
Proj" variant, we observe a characteristic asymmetric performance
degradation where Kappa (0.347) experiences a 57.7\% reduction while
Precision (0.455) shows only a 44.5\% decrease. This disproportionate
effect indicates that weight projection particularly enhances the
model\textquotesingle s ability to maintain performance consistency
across classes rather than purely boosting classification accuracy.
Similarly, the "No Feature Proj" variant shows a more balanced
degradation pattern across metrics, suggesting that feature projection
contributes more holistically to the model\textquotesingle s performance
landscape.

The attention mechanism variants reveal particularly interesting
metric-specific impacts. The "Single Head Attn" configuration maintains
relatively higher Precision (0.594) compared to Kappa (0.515), with a
performance differential of 7.9 percentage points. This pattern
indicates that multi-head attention specifically enhances the
model\textquotesingle s ability to generalize across different data
distributions rather than simply improving point-wise classification
precision. In contrast, the "No Residual" variant shows more uniform
performance degradation across metrics, suggesting that residual
connections contribute foundationally to all aspects of model
performance rather than specializing in specific capabilities.

Feature fusion variants exhibit the most complex metric interaction
patterns. The "Alternative Fusion" approach maintains near-baseline
performance in MCC (0.816) and Kappa (0.767) while showing more
noticeable degradation in F1 Score (0.755). This selective preservation
of certain metrics suggests that alternative fusion strategies may offer
significant computational efficiency with minimal impact on the
model\textquotesingle s discrimination capabilities, particularly for
distinguishing between difficult cases. Conversely, the "No Feature
Fusion" variant shows substantial degradation across all metrics, but
with particular weakness in Kappa (0.546) compared to Precision (0.619),
indicating that dedicated feature integration is especially important
for maintaining consistent performance across diverse data classes.

The loss function variants reveal that the choice between dynamic focal
loss and cross-entropy affects different aspects of model performance.
The "Cross Entropy" variant maintains relatively higher Precision
(0.662) compared to Recall (0.627), while the baseline dynamic focal
loss shows more balanced performance across these metrics. This pattern
suggests that dynamic focal loss specifically enhances the
model\textquotesingle s ability to maintain high recall without
sacrificing precision, a critical capability for seismic anomaly
detection where false negatives can have significant consequences.

Position encoding variants demonstrate the most subtle metric
interactions. While removing position encoding causes moderate
performance degradation across all metrics, the "Learnable Position"
variant actually improves performance in certain dimensions,
particularly MCC (0.826) and Accuracy (0.807). This improvement suggests
that learnable position embeddings can better adapt to the specific
structural patterns in MBT data compared to fixed sinusoidal encodings,
highlighting an opportunity for architectural refinement.

The training strategy radar patterns reveal that optimization dynamics
disproportionately affect different aspects of model performance. The
"No Warmup" variant shows significantly greater degradation in F1 Score
(0.659) and Recall (0.688) compared to MCC (0.769), indicating that
warmup periods particularly enhance the model\textquotesingle s ability
to detect positive cases in imbalanced datasets. Similarly, the "No
Decay" variant shows pronounced weakness in F1 Score (0.591) while
better preserving MCC (0.767), suggesting that learning rate decay
strategies specifically contribute to balancing precision and recall
rather than simply improving overall accuracy.

These diverse metric interaction patterns across different ablation
variants provide invaluable insights into the specialized functions of
each architectural component. Components showing uniform degradation
patterns across metrics (such as weight projection) represent
foundational elements of the architecture that cannot be simplified
without comprehensive performance penalties. In contrast, components
showing selective metric preservation (such as alternative fusion or
learnable position encoding) represent opportunities for architectural
refinement that could maintain essential performance characteristics
while reducing computational complexity.

4.4.3 Integrated Component Analysis

To obtain a holistic understanding of component interactions, we
conducted an integrated analysis across multiple dimensions, as shown in
Figure 12. The component impact assessment (Figure 12a) quantifies the
percentage reduction in MCC when each component is removed or modified,
revealing a clear hierarchy of architectural importance: weight
projection (48.9\%) \textgreater{} feature projection (41.4\%)
\textgreater{} additive fusion (31.2\%) \textgreater{} single-head
attention (27.8\%) \textgreater{} dynamic focal loss (25.0\%).

\includegraphics[width=6.14167in,height=5.08125in]{./media/media/image12.png}

Figure . Comprehensive ablation analysis of the WE-FTT model: (a)
Component Impact on Performance quantifies MCC reduction when removing
each component, with weight projection showing the highest impact
(48.9\%); (b) Model Component Impact Analysis evaluates components
across three metrics: MCC Reduction Rate, Error Asymmetry, and Column
Bias; (c) Component Effectiveness Analysis plots MCC against
computational complexity, revealing optimal efficiency in the
upper-right quadrant; (d) Class Sensitivity Analysis demonstrates how
component removal differentially affects performance across feature
classes, with classes 0, 3, 4, and 9 showing the highest sensitivity to
architecture changes.

The multi-metric component analysis (Figure 12b) evaluates each major
architectural group across three complementary metrics: MCC reduction
rate, error asymmetry, and column bias in the confusion matrix. This
analysis reveals that while input projection components primarily affect
overall accuracy through their role in establishing properly weighted
feature representations, attention mechanisms have a more pronounced
impact on error distributions and bias patterns. Feature fusion shows a
particularly strong influence on column bias (89.2\%), indicating its
role in balancing predictions across classes.

The component effectiveness analysis (Figure 12c) plots performance
(MCC) against computational complexity, providing insights into the
efficiency frontier of our architecture. Components in the upper-right
quadrant (learnable position encoding, alternative fusion) offer
favorable performance-complexity trade-offs, while those in the
lower-left (no weight projection, no feature projection) represent
critical components that cannot be simplified without substantial
performance penalties.

The class sensitivity analysis (Figure 12d) demonstrates how component
removal differentially affects performance across various feature
classes. Notably, classes 0, 3, 4, and 9 show the highest sensitivity to
architectural changes, while classes 2 and 7 remain relatively stable.
Weight projection demonstrates the most consistent impact across all
classes, with peak sensitivity in classes 0, 3, 5, and in particular
class 9, highlighting its crucial role in modeling complex feature
interactions.

4.4.4 Ablation Study Implications

The comprehensive ablation analysis yields several significant
implications for the development and optimization of microwave-based
seismic anomaly detection models:

1) The dual-pathway architecture with specialized weight and feature
projections represents the most critical innovation in our approach,
with their combined removal resulting in over 90\% performance
degradation. This confirms the fundamental importance of explicitly
transforming pre-computed weights and features into a common embedding
space for element-wise multiplication before attention computation,
rather than attempting to incorporate weights directly within the
attention mechanism itself.

2) Attention mechanisms, particularly when combined with residual
connections, provide substantial benefits for capturing complex
interactions among frequency channels, but their specific implementation
(multi-head vs. single-head) offers some flexibility for parameter
efficiency optimization.

3) The training dynamics, including loss function design and
optimization scheduling, play a more significant role than typically
acknowledged in tabular data modeling, with properly configured dynamic
focal loss and warmup strategies providing cumulative performance
improvements of approximately 35\%.

4) Position encoding, while standard in transformer architectures,
contributes relatively minor benefits in our specific application,
suggesting potential for architectural simplification in future
iterations.

These findings quantify the contribution of individual architectural
components to the overall performance of the Weight-Enhanced
FT-Transformer architecture.

5. Discussion

5.1 Summary of Key Findings

Our research employed an innovative methodological framework to analyze
AMSR-2 microwave brightness temperature data across diverse
environmental zones, revealing several significant findings:

First, we identified environment-specific optimal frequency-polarization
combinations that achieve remarkably high detection reliability across
different landscapes. Marine environments (Zone A) demonstrated optimal
detection through 89GHz H-polarization and 36.5GHz V-polarization
combinations (support: 1.0000); humid forests (Zone B) achieved perfect
reliability with 89GHz V-polarization and 36.5GHz V-polarization; dry
forests (Zone C) showed best performance with 36.5GHz H-polarization and
10.65GHz V-polarization; wetlands (Zone D) exhibited exceptional
sensitivity with combinations incorporating 6.9GHz H-polarization; while
arid regions (Zone E) achieved perfect detection reliability through
23.8GHz H-polarization and 36.5GHz V-polarization combinations.

Second, our Weight-Enhanced Feature-based Transformer (WE-FTT) model
demonstrated superior performance across all evaluation metrics compared
to traditional machine learning approaches, achieving an average
classification accuracy of 84.2\%. The model showed remarkable
improvements in Matthews Correlation Coefficient (0.84), significantly
outperforming the next best model, RandomForest (0.74), demonstrating
its enhanced ability to resolve signal ambiguities inherent in MBT data.

Third, our comprehensive ablation study confirmed that the
weight-enhanced dual projection pathway represents the most critical
architectural innovation, accounting for approximately 50\% of the
model\textquotesingle s performance. This validates our hypothesis that
explicit integration of frequency itemset mining results significantly
enhances seismic anomaly detection capabilities.

5.2 Physical Mechanisms and Interpretations

The identified pre-seismic anomaly patterns reveal fundamental insights
into the physical mechanisms of seismic precursor generation and
detection across diverse environmental conditions. The achievement of
near-perfect detection reliability through environment-specific
frequency combinations demonstrates both the robustness of
microwave-based precursor detection and the presence of coherent
pre-seismic phenomena at multiple spatial scales.

The systematic variation in optimal anomaly detection combinations
across zones reflects distinct pre-seismic mechanisms. These specific
frequency-polarization combinations likely represent the most robust and
less ambiguous indicators of pre-seismic changes within each
environment, which the WE-FTT learns to prioritize, overcoming noise and
variability in other channels. Marine zones exhibit precursor signatures
through high-frequency combinations sensitive to surface condition
changes, while humid forests require V-polarization dominance for
detecting precursor signals through vegetation. The quantified
degradation in detection reliability with additional frequencies in
humid forests (Δsupport ≈ 0.0003) provides a precise measure of
environmental interference effects.

Temperature range distributions reveal sophisticated precursor signal
characteristics that vary systematically with environmental conditions.
The consistent broadening of anomaly detection ranges in low-frequency
channels, particularly evident in wetland (Δt ≈ 93K) and arid zones (Δt
≈ 154K), indicates detection capability across multiple depths,
modulated by environmental dielectric properties. The observation of
critical temperature overlap regions (e.g., 159-207K in dry forests)
defines optimal detection windows specific to each environment.

The polarization-dependent behavior demonstrates sophisticated precursor
detection mechanisms across zones. The superior performance of
H-polarization in structured environments and V-polarization in
moisture-rich conditions reveals fundamental differences in pre-seismic
signal generation and propagation. This polarization sensitivity
provides a powerful tool for discriminating genuine precursor signals
from environmental noise.

Of particular significance is the emergence of hierarchical detection
relationships in wetland environments, where perfect reliability is
achieved through multiple distinct pathways. This suggests the presence
of multi-layer pre-seismic phenomena that can be exploited for enhanced
detection sensitivity. The systematic temperature range stratification
(Δt varying from 93K to 214K) enables depth-resolved precursor
monitoring through strategic frequency selection.

These phenomena can be explained through the lens of seismic preparation
processes: as stress accumulates in the lithosphere prior to rupture,
various physical and chemical changes occur near the epicentral region.
These include: (1) microfracturing processes that alter near-surface
porosity and moisture content {[}49{]}, detectable by low-frequency
channels in zones with minimal vegetation; (2) gas emission (radon,
methane, CO2) that modifies surface dielectric properties, particularly
evident in the frequency-dependent responses in arid zones {[}50{]}; and
(3) localized thermal anomalies resulting from increased friction or
fluid migration, captured most effectively by V-polarization channels in
moisture-rich environments {[}51{]}.

5.3 Comparison with Existing Literature on Seismic Precursors

Our research extends the work of Liu et al. {[}36{]} on MBT anomalies in
the Qinghai-Tibet Plateau by providing the first global-scale analysis
of environment-specific frequency combinations for seismic precursor
detection. Compared to the wavelet maxima analysis approach employed by
Wu et al. {[}23{]}, our frequency itemset mining method achieves higher
detection reliability, particularly in identifying environment-specific
frequency-polarization combinations with near-perfect support values.
Crucially, many previous methods often struggled with the inherent
signal ambiguity and lacked mechanisms, such as our environment-specific
knowledge integration, to effectively address it, contributing to
inconsistent findings reported in the literature. While multi-parameter
studies {[}e.g., Ref 1, 4, 28{]} can provide stronger corroborative
evidence by integrating diverse data streams, our MBT-focused analysis
offers unique, in-depth insights into the specific information content,
optimal channel selection, and environment-dependent behavior of
microwave emissions potentially linked to seismic activity.

The WE-FTT model demonstrates clear advantages in integrating
multi-frequency microwave information compared to traditional
approaches. Unlike the wavelet analysis used by Wu et al. and the
threshold-based detection method employed by Liu et al., our model
adaptively adjusts the importance of different frequency channels based
on their relevance to seismic activity in each zone, resulting in higher
detection accuracy and lower false positive rates.

Notably, our identified frequency-polarization patterns align with
previous studies in some aspects, such as the importance of
high-frequency bands (36.5GHz and 89GHz) across multiple terrain
conditions {[}20,25{]}, but our research is the first to explicitly
quantify the relative contribution of frequency channels under different
environmental conditions. This environment-specific optimization
addresses a critical gap in previous literature, which often applied
uniform detection thresholds across heterogeneous landscapes.

Our approach also advances beyond previous work by implementing a
comprehensive feature-weight integration scheme that incorporates domain
knowledge from data mining directly into the neural network
architecture. This represents a significant improvement over traditional
methods that typically treat feature selection and model training as
separate processes, enabling more effective exploitation of the complex
relationships between microwave frequencies and seismic activity.

5.4 Limitations of the Study

Despite the significant achievements of our approach, several important
limitations must be acknowledged:

1) Earthquake Catalog Dependency: Our analysis relies solely on
earthquake events with M≥7.0 from the USGS catalog, potentially
overlooking smaller yet locally significant events that might exhibit
detectable MBT anomalies under specific environmental conditions.

2) Potential Confounding Factors: Extreme weather events, sudden
anthropogenic activities, or seasonal variations may produce microwave
brightness temperature patterns similar to seismic precursors, which are
not fully addressed in the current methodology. The
model\textquotesingle s ability to distinguish between seismic and
non-seismic anomalies requires further validation in diverse real-world
scenarios.

3) Dobrovolsky Radius Assumption: We employed the classical Dobrovolsky
radius formula (R = 10\^{}0.43M km) to define the preparation zone for
earthquakes, but this formula may not be universally applicable across
all geological settings, particularly in marine environments where wave
propagation characteristics differ significantly from continental
regions.

4) Data Processing Limitations: While efficient for large-scale data
analysis, the K-means clustering and Apriori algorithms have inherent
limitations in handling high-dimensional non-linear relationships,
potentially causing some complex patterns to be overlooked. The
discretization process necessary for association rule mining may also
introduce artificial boundaries in the continuous MBT data.

5) Interpretation of Fill Values: Our analysis included standard AMSR-2
fill values (655.35 K, flagging invalid/missing data) {[}52{]},
potentially influencing the upper bounds of some derived clusters
(Section 4.2). However, core findings regarding optimal channels and
support differences depend mainly on valid data distributions and
cluster lower bounds. This limitation highlights the need for rigorous
pre-filtering of fill values and flagged data in future work to ensure
physical validity and avoid artifacts.

6) Focus on M≥7.0 Events: Although preliminary testing indicated that
smaller events lacked consistent MBT anomalies, the exclusion of
sub-M7.0 earthquakes may lead to overlooking valuable precursory
patterns that might exist under specific environmental or geological
conditions.

7) Temporal Resolution Constraints: The daily aggregation of MBT data
may obscure short-term fluctuations potentially relevant to seismic
precursor detection, particularly for rapidly evolving anomalies that
manifest only hours before an earthquake.

8) Ambiguity Resolution Limits: While the WE-FTT model shows improved
performance, it may still misclassify genuinely ambiguous cases where
MBT signals strongly mimic seismic precursors due to extreme NbNS
factors or vice-versa. Fully resolving all signal ambiguities likely
requires incorporating complementary datasets.

9) Single Data Source Reliance: This study focuses exclusively on MBT
data. While this allows for deep exploration of microwave signal
characteristics and development of specialized methodologies, it
inherently lacks the comprehensiveness of multi-parameter approaches.
Potential seismic anomalies identified solely from MBT might lack
corroboration from other geophysical or geochemical indicators,
potentially increasing the false positive rate. Future work integrating
complementary datasets is essential for validation and improving
diagnostic certainty. However, this study provides a crucial baseline
for understanding the capabilities and optimal utilization of MBT data
itself within the broader context of earthquake precursor research.

10) Focus on M≥7.0 Events" and enhance: "Focus on M≥7.0 Events: Our
analysis relies solely on major earthquakes (M≥7.0). While justified by
the apparent lack of consistent signals for smaller events in
preliminary global tests, this focus necessarily limits the
study\textquotesingle s scope and direct applicability to detecting
precursors for more frequent, moderate-magnitude earthquakes, which
might exhibit detectable anomalies under specific local conditions.

5.5 Comparison with Relevant Literature on MBT Seismic Precursors

Our findings complement existing research on
lithosphere-atmosphere-ionosphere coupling physics. Compared to studies
focusing on single frequencies, our multi-frequency, multi-polarization
analysis provides a more comprehensive framework for seismic precursor
detection. Our approach particularly emphasizes the modulating effect of
environmental conditions on detection capabilities, an aspect
insufficiently explored in previous research.

The broad anomaly detection ranges observed in low-frequency channels in
wetland and arid zones (Δt ranging from 93K to 154K) support the P-hole
activation hypothesis, which suggests that pre-seismic changes in rock
stress lead to alterations in groundwater levels, subsequently affecting
surface dielectric properties. Our findings also align with the gas
emission mechanism, which proposes that pre-seismic release of gases
(such as radon) modifies surface conditions, creating detectable
microwave brightness temperature changes, particularly in arid regions.

The superior performance of high-frequency channels in marine zones is
consistent with the thermal anomaly hypothesis, which suggests that
pre-seismic thermal anomalies can be detected by high-frequency
microwave channels through subtle effects on sea surface temperature.
This provides empirical support for theoretical models of thermal energy
transfer from seismic preparation zones to the surface environment.

Our zone-specific optimization approach addresses a fundamental
limitation identified by Maeda and Takano {[}18{]}, who emphasized the
challenge of discriminating local seismic changes from ambient
fluctuations. By establishing environment-specific detection windows and
optimal frequency-polarization combinations, our methodology provides a
robust framework for enhancing signal-to-noise ratios in diverse
landscapes.

The WE-FTT model\textquotesingle s architecture represents an
advancement over previous machine learning approaches applied to seismic
precursor detection. Unlike the wavelet-based methods employed by Wu et
al. {[}23{]}, which operate primarily on single-channel time series, our
model effectively captures complex interactions between multiple
frequency channels through its weight-enhanced attention mechanism. This
enables more sophisticated pattern recognition capabilities,
particularly for subtle anomalies that manifest across multiple
frequencies simultaneously.

5.6 Future Research Directions

Based on the results and limitations of this study, we propose several
promising directions for future research:

1) Validation on independent datasets and different geographical
regions, particularly in seismically active areas with complex
environmental conditions that may challenge the current zonation
approach. This would help establish the generalizability of the
identified frequency-polarization patterns and assess the robustness of
the WE-FTT model under diverse geological settings.

2) Integration of complementary data sources: Incorporating
physically-based constraints from other monitoring techniques (e.g.,
crustal strain from GNSS, groundwater level changes, atmospheric gas
concentrations, ionospheric TEC) is crucial to validate MBT anomalies,
reduce false positives, and build a more comprehensive understanding of
the underlying LAIC processes.

3) Extension of the analysis to include M\textless7.0 earthquake events,
with particular focus on moderate-magnitude earthquakes that may exhibit
consistent anomaly patterns under specific environmental conditions.
This would expand the applicability of the methodology to more frequent
seismic events and potentially increase the statistical significance of
the findings.

4) Exploration of real-time implementation possibilities, developing
high-reliability seismic precursor detection systems with adaptive
monitoring approaches that dynamically adjust the importance of
different frequency-polarization combinations based on local
environmental conditions. This could involve the development of
specialized hardware configurations optimized for the most effective
frequency combinations in each geographical region.

5) Refinement of the WE-FTT model architecture, particularly optimizing
the position encoding and feature fusion components identified as
potentially redundant in the ablation study. This architectural
streamlining could enhance computational efficiency while maintaining
detection performance, facilitating deployment on resource-constrained
platforms for widespread monitoring applications.

6) Investigation of multiscale temporal patterns in MBT data, exploring
both shorter-term (hourly) fluctuations and longer-term (seasonal)
variations to develop a more comprehensive understanding of the temporal
evolution of seismic precursors. This could involve the implementation
of hierarchical temporal attention mechanisms within the WE-FTT
architecture to capture patterns at multiple time scales simultaneously.

6. Conclusions

This study presents a comprehensive methodological framework for
detecting seismic precursors using microwave brightness temperature
(MBT) data across diverse environmental conditions. Through systematic
analysis of AMSR-2 data spanning 2013-2023 and integration of multiple
analytical approaches, we have made several significant contributions to
the field of remote sensing-based earthquake monitoring:

First, we have identified environment-specific optimal
frequency-polarization combinations that achieve exceptional detection
reliability across five distinct surface types. Each environmental zone
exhibited unique microwave response characteristics, with marine
environments showing highest sensitivity to 89GHz H-polarization and
36.5GHz V-polarization combinations, while terrestrial zones
demonstrated varying preferences based on vegetation coverage and soil
moisture conditions. These findings establish critical detection windows
for each environment, enabling targeted monitoring approaches with
enhanced signal-to-noise ratios.

Second, we developed a novel Weight-Enhanced Feature-based Transformer
(WE-FTT) model that successfully integrates domain knowledge from
frequency itemset mining into a deep learning architecture. This model
demonstrated superior performance across all evaluation metrics compared
to traditional approaches, achieving an average classification accuracy
of 84.2\% and consistently outperforming the next best model by
substantial margins (9-13\% across different metrics). The comprehensive
ablation study confirmed that the weight-enhanced dual projection
pathway represents the most critical architectural innovation,
validating our approach to explicit integration of mining-derived
weights.

Third, we established a quantitative framework for understanding the
physical relationships between seismic activity and microwave emissions
across diverse landscapes. The systematic patterns observed in optimal
frequency-polarization combinations and temperature range distributions
provide valuable insights into the underlying mechanisms of seismic
precursor generation and propagation, potentially contributing to
improved theoretical models of lithosphere-atmosphere-ionosphere
coupling.

While our approach demonstrates significant advances in seismic
precursor detection, important challenges remain for future research.
These include validation on independent datasets, extension to
moderate-magnitude earthquakes, exploration of real-time implementation
possibilities, and further refinement of architectural components.
Additionally, the integration of complementary data sources and
physically-based constraints could enhance the specificity and
reliability of anomaly detection.

In conclusion, this research establishes a robust foundation for
environment-adaptive seismic monitoring through microwave remote
sensing. By revealing the complex relationships between frequency
channel sensitivity and environmental conditions, and by developing
advanced computational frameworks for anomaly detection, this work
contributes to the ongoing advancement of earthquake early warning
capabilities with potential implications for disaster risk reduction in
seismically active regions worldwide.

References

1. Akhoondzadeh, M.; De Santis, A.; Marchetti, D.; Piscini, A.;
Cianchini, G. Multi Precursors Analysis Associated with the Powerful
Ecuador (MW= 7.8) Earthquake of 16 April 2016 Using Swarm Satellites
Data in Conjunction with Other Multi-Platform Satellite and Ground Data.
\emph{Advances in Space Research} \textbf{2018}, \emph{61}, 248--263,
doi:10.1016/j.asr.2017.07.014.

2. Dobrovolsky, I.P.; Zubkov, S.I.; Miachkin, V.I. Estimation of the
Size of Earthquake Preparation Zones. \emph{PAGEOPH} \textbf{1979},
\emph{117}, 1025--1044, doi:10.1007/BF00876083.

3. Singh, R.P.; Mehdi, W.; Gautam, R.; Senthil Kumar, J.; Zlotnicki, J.;
Kafatos, M. Precursory Signals Using Satellite and Ground Data
Associated with the Wenchuan Earthquake of 12 May 2008.
\emph{International Journal of Remote Sensing} \textbf{2010}, \emph{31},
3341--3354, doi:10.1080/01431161.2010.487503.

4. Pulinets, S.; Ouzounov, D. Lithosphere--Atmosphere--Ionosphere
Coupling (LAIC) Model -- an Unified Concept for Earthquake Precursors
Validation. \emph{Journal of Asian Earth Sciences} \textbf{2011},
\emph{41}, 371--382, doi:10.1016/j.jseaes.2010.03.005.

5. Wu, L.; Qin, K.; Liu, S. GEOSS-Based Thermal Parameters Analysis for
Earthquake Anomaly Recognition. \emph{Proc. IEEE} \textbf{2012},
\emph{100}, 2891--2907, doi:10.1109/JPROC.2012.2184789.

6. Carver, K.; Elachi, C.; Ulaby, F. Microwave Remote Sensing from
Space. \emph{Proceedings of the IEEE} \textbf{1985}, \emph{73},
970--996, doi:10.1109/PROC.1985.13230.

7. Hersbach, H.; Bell, B.; Berrisford, P.; Hirahara, S.; Horányi, A.;
Muñoz-Sabater, J.; Nicolas, J.; Peubey, C.; Radu, R.; Schepers, D.; et
al. The ERA5 Global Reanalysis. \emph{Quarterly Journal of the Royal
Meteorological Society} \textbf{2020}, \emph{146}, 1999--2049,
doi:10.1002/qj.3803.

8. Kawanishi, T.; Sezai, T.; Ito, Y.; Imaoka, K.; Takeshima, T.; Ishido,
Y.; Shibata, A.; Miura, M.; Inahata, H.; Spencer, R.W. The Advanced
Microwave Scanning Radiometer for the Earth Observing System (AMSR-E),
NASDA's Contribution to the EOS for Global Energy and Water Cycle
Studies. \emph{IEEE Trans. Geosci. Remote Sensing} \textbf{2003},
\emph{41}, 184--194, doi:10.1109/TGRS.2002.808331.

9. Njoku, E.G.; Li Li Retrieval of Land Surface Parameters Using Passive
Microwave Measurements at 6-18 GHz. \emph{IEEE Trans. Geosci. Remote
Sensing} \textbf{1999}, \emph{37}, 79--93, doi:10.1109/36.739125.

10. Freund, F. Time‐resolved Study of Charge Generation and Propagation
in Igneous Rocks.

11. Jing, F.; Xu, Y.; Singh, R.P. Changes in Surface Water Bodies
Associated with Madoi (China) Mw 7.3 Earthquake of May 21, 2021 Using
Sentinel-1 Data. \emph{IEEE Trans. Geosci. Remote Sensing}
\textbf{2022}, \emph{60}, 1--11, doi:10.1109/TGRS.2022.3170890.

12. Mao, W.; Wu, L.; Qi, Y. Impact of Compressive Stress on Microwave
Dielectric Properties of Feldspar Specimen. \emph{IEEE Trans. Geosci.
Remote Sensing} \textbf{2020}, \emph{58}, 1398--1408,
doi:10.1109/TGRS.2019.2946155.

13. Qi, Y.; Wu, L.; He, M.; Mao, W. Spatio-Temporally Weighted Two-Step
Method for Retrieving Seismic MBT Anomaly: May 2008 Wenchuan Earthquake
Sequence Being a Case. \emph{IEEE J. Sel. Topics Appl. Earth Observ.
Remote Sens.} \textbf{2020}, \emph{13}, 382--391,
doi:10.1109/JSTARS.2019.2962719.

14. Takano, T.; Maeda, T. Experiment and Theoretical Study of Earthquake
Detection Capability by Means of Microwave Passive Sensors on a
Satellite. \emph{IEEE Geosci. Remote Sens. Lett.} \textbf{2009},
\emph{6}, 107--111, doi:10.1109/LGRS.2008.2005735.

15. Keiji Imaoka; Takashi Maeda; Misako Kachi; Marehito Kasahara;
Norimasa Ito; Keizo Nakagawa Status of AMSR2 Instrument on GCOM-W1.;
November 9 2012; Vol. 8528, p. 852815.

16. Liu, S.; Xu, Z.; Wei, J.; Huang, J.; Wu, L. Experimental Study on
Microwave Radiation from Deforming and Fracturing Rock under Loading
Outdoor. \emph{IEEE Trans. Geosci. Remote Sensing} \textbf{2016},
\emph{54}, 5578--5587, doi:10.1109/TGRS.2016.2569419.

17. Gupta, M.; Scharien, R.; Barber, D. Microwave Emission and
Scattering from Ocean Surface Waves in the Southern Beaufort Sea.
\emph{International Journal of Oceanography} \textbf{2014}, \emph{2014},
1--12, doi:10.1155/2014/872342.

18. Maeda, T.; Takano, T. Discrimination of Local and Faint Changes from
Satellite-Borne Microwave-Radiometer Data. \emph{IEEE Trans. Geosci.
Remote Sensing} \textbf{2008}, \emph{46}, 2684--2691,
doi:10.1109/TGRS.2008.919144.

19. Qi, Y.; Wu, L.; Ding, Y.; Mao, W. Microwave Brightness Temperature
Anomalies Associated with the 2015 Mw 7.8 Gorkha and Mw 7.3 Dolakha
Earthquakes in Nepal. \emph{IEEE Trans. Geosci. Remote Sens.}
\textbf{2022}, \emph{60}, 1--11, doi:10.1109/TGRS.2020.3036079.

20. Jing, F.; Singh, R.P.; Sun, K.; Shen, X. Passive Microwave Response
Associated with Two Main Earthquakes in Tibetan Plateau, China.
\emph{Adv. Space Res.} \textbf{2018}, \emph{62}, 1675--1689,
doi:10.1016/j.asr.2018.06.030.

21. Ma, Y.; Liu, S.; Wu, L.; Xu, Z. Two-Step Method to Extract Seismic
Microwave Radiation Anomaly: Case Study of MS8.0 Wenchuan Earthquake.
\emph{Earthq Sci} \textbf{2011}, \emph{24}, 577--582,
doi:10.1007/s11589-011-0819-x.

22. Qi, Y.; Wu, L.; Mao, W.; Ding, Y.; Liu, Y.; Wang, X. Characteristic
Background of Microwave Brightness Temperature (MBT) and Optimal
Microwave Channels for Searching Seismic MBT Anomaly in and around the
Qinghai-Tibet Plateau. \emph{IEEE Trans. Geosci. Remote Sensing}
\textbf{2023}, \emph{61}, 1--18, doi:10.1109/TGRS.2023.3299643.

23. Wu, H.; Xiong, P.; Chen, J.; Zhang, X.; Yang, X. Identifying Seismic
Anomalies via Wavelet Maxima Analysis of Satellite Microwave Brightness
Temperature Observations. \emph{Remote Sensing} \textbf{2024},
\emph{16}, 303, doi:10.3390/rs16020303.

24. Lixin, W.; Yuan, Q.; Wenfei, M.; Shanjun, L.; Yifan, D.; Feng, J.;
Xuhui, S. Progresses and Possible Frontiers in the Study on Seismic
Applications of Multi-Frequency and Multi-Polarization Passive Microwave
Remote Sensing. \emph{Acta Geodaetica et Cartographica Sinica}
\textbf{2022}, \emph{51}, 1356--1371.

25. Jing, F.; Singh, R.P.; Shen, X. Land -- Atmosphere -- Meteorological
Coupling Associated with the 2015 Gorkha (M 7.8) and Dolakha (M 7.3)
Nepal Earthquakes. \emph{Geomatics, Natural Hazards and Risk}
\textbf{2019}.

26. Owe, M.; De Jeu, R.; Walker, J. A Methodology for Surface Soil
Moisture and Vegetation Optical Depth Retrieval Using the Microwave
Polarization Difference Index. \emph{IEEE Trans. Geosci. Remote Sens.}
\textbf{2001}, \emph{39}, 1643--1654, doi:10.1109/36.942542.

27. Jeong, S.; Lee, W.K.; Kil, H.; Jang, S.; Kim, J.; Kwak, Y. Deep
Learning‐Based Regional Ionospheric Total Electron Content
Prediction---Long Short‐Term Memory (LSTM) and Convolutional LSTM
Approach. \emph{Space Weather} \textbf{2024}, \emph{22}, e2023SW003763,
doi:10.1029/2023SW003763.

28. Liu, Y.; Wu, L.; Qi, Y.; Ding, Y. General Features of
Multi-Parameter Anomalies of Six Moderate Earthquakes Occurred near
Zhangbei-Bohai Fault in China during the Past Decades. \emph{Remote
Sens. Environ.} \textbf{2023}, \emph{295}, 113692,
doi:10.1016/j.rse.2023.113692.

29. Freund, F. Pre-Earthquake Signals: Underlying Physical Processes.
\emph{Journal of Asian Earth Sciences} \textbf{2011}, \emph{41},
383--400, doi:10.1016/j.jseaes.2010.03.009.

30. Mao, W.; Wu, L.; Liu, S.; Gao, X.; Huang, J.; Xu, Z.; Qi, Y.
Additional Microwave Radiation from Experimentally Loaded Granite
Covered with Sand Layers: Features and Mechanisms. \emph{IEEE
Transactions on Geoscience and Remote Sensing} \textbf{2020}, \emph{58},
5008--5022, doi:10.1109/TGRS.2020.2971465.

31. Hayakawa, M.; Hobara, Y. Integrated Analysis of Multi-Parameter
Precursors to the Fukushima Offshore Earthquake (Mj = 7.3) on 13
February 2021 and Lithosphere--Atmosphere--Ionosphere Coupling Channels.
\emph{Atmosphere} \textbf{2024}, \emph{15}, 1015,
doi:10.3390/atmos15081015.

32. Kasahara, M.; Imaoka, K.; Kachi, M.; Fujii, H.; Naoki, K.; Maeda,
T.; Ito, N.; Nakagawa, K.; Oki, T. Status of AMSR2 on GCOM-W1. In
Proceedings of the Sensors, Systems, and Next-Generation Satellites XVI;
SPIE, November 19 2012; Vol. 8533, pp. 31--40.

33. Kachi, M.; Imaoka, K.; Fujii, H.; Shibata, A.; Kasahara, M.; Iida,
Y.; Ito, N.; Nakagawa, K.; Shimoda, H. Status of GCOM-W1/AMSR2
Development and Science Activities. \textbf{2008}, \emph{7106},
doi:10.1117/12.801228.

34. Fang, H.; Beaudoing, H.K.; Rodell, matthew; Teng, W.L.; Vollmer,
B.E. Global Land Data Assimilation System (GLDAS) Products, Services and
Application from NASA Hydrology Data and Information Services Center
(HDISC).; Baltimore, MD, January 1 2009.

35. Muñoz-Sabater, J.; Dutra, E.; Agustí-Panareda, A.; Albergel, C.;
Arduini, G.; Balsamo, G.; Boussetta, S.; Choulga, M.; Harrigan, S.;
Hersbach, H.; et al. ERA5-Land: A State-of-the-Art Global Reanalysis
Dataset for Land Applications. \emph{Earth System Science Data}
\textbf{2021}, \emph{13}, 4349--4383, doi:10.5194/essd-13-4349-2021.

36. Liu, S.; Cui, Y.; Wei, L.; Liu, W.; Ji, M. Pre-Earthquake MBT
Anomalies in the Central and Eastern Qinghai-Tibet Plateau and Their
Association to Earthquakes. \emph{Remote Sens. Environ.} \textbf{2023},
\emph{298}, 113815, doi:10.1016/j.rse.2023.113815.

37. Han, J.; Kamber, M.; Mining, D. Concepts and Techniques.
\emph{Morgan kaufmann} \textbf{2006}, \emph{340}, 94104--103205.

38. Thorndike, R.L. Who Belongs in the Family? \emph{Psychometrika}
\textbf{1953}, \emph{18}, 267--276, doi:10.1007/BF02289263.

39. Rousseeuw, P.J. Silhouettes: A Graphical Aid to the Interpretation
and Validation of Cluster Analysis. \emph{Journal of Computational and
Applied Mathematics} \textbf{1987}, \emph{20}, 53--65,
doi:10.1016/0377-0427(87)90125-7.

40. Agrawal, R.; Imieliński, T.; Swami, A. Mining Association Rules
between Sets of Items in Large Databases. \emph{Proceedings of the 1993
ACM SIGMOD international conference on Management of data} 1993,
207--216.

41. Han, J.; Pei, J.; Yin, Y. Mining Frequent Patterns without Candidate
Generation. \emph{Proceedings of the 2000 ACM SIGMOD international
conference on Management of data} 2000, 1--12.

42. Gorishniy, Y.; Rubachev, I.; Khrulkov, V.; Babenko, A. Revisiting
Deep Learning Models for Tabular Data. In Proceedings of the Proceedings
of the 35th International Conference on Neural Information Processing
Systems; Curran Associates Inc., 2021; p. Article 1447.

43. Vaswani, A.; Shazeer, N.; Parmar, N.; Uszkoreit, J.; Jones, L.;
Gomez, A.N.; Kaiser, Ł.; Polosukhin, I. Attention Is All You Need. In
Proceedings of the Proceedings of the 31st International Conference on
Neural Information Processing Systems; Curran Associates Inc.: Long
Beach, California, USA, 2017; pp. 6000--6010.

44. Matthews, B.W. Comparison of the Predicted and Observed Secondary
Structure of T4 Phage Lysozyme. \emph{Biochimica et Biophysica Acta
(BBA) - Protein Structure} \textbf{1975}, \emph{405}, 442--451,
doi:10.1016/0005-2795(75)90109-9.

45. Cohen, J. A Coefficient of Agreement for Nominal Scales.
\emph{Educational and Psychological Measurement} \textbf{1960},
\emph{20}, 37--46, doi:10.1177/001316446002000104.

46. Akiba, T.; Sano, S.; Yanase, T.; Ohta, T.; Koyama, M. Optuna: A
next-Generation Hyperparameter Optimization Framework. \emph{Proceedings
of the 25th ACM SIGKDD International Conference on Knowledge Discovery
\& Data Mining} 2019, 2623--2631.

47. Arik, S.Ö.; Pfister, T. TabNet: Attentive Interpretable Tabular
Learning. \emph{AAAI} \textbf{2021}, \emph{35}, 6679--6687,
doi:10.1609/aaai.v35i8.16826.

48. Prokhorenkova, L.; Gusev, G.; Vorobev, A.; Dorogush, A.V.; Gulin, A.
CatBoost: Unbiased Boosting with Categorical Features. In Proceedings of
the Proceedings of the 32nd International Conference on Neural
Information Processing Systems; Curran Associates Inc.: Montréal,
Canada, 2018; pp. 6639--6649.

49. Scholz, C.H.; Sykes, L.R.; Aggarwal, Y.P. Earthquake Prediction: A
Physical Basis. \emph{Science} \textbf{1973}, \emph{181}, 803--810,
doi:10.1126/science.181.4102.803.

50. King, C.-Y. Gas Geochemistry Applied to Earthquake Prediction: An
Overview. \emph{Journal of Geophysical Research: Solid Earth}
\textbf{1986}, \emph{91}, 12269--12281, doi:10.1029/JB091iB12p12269.

51. Tronin, A.A.; Hayakawa, M.; Molchanov, O.A. Thermal IR Satellite
Data Application for Earthquake Research in Japan and China.
\emph{Journal of Geodynamics} \textbf{2002}, \emph{33}, 519--534,
doi:10.1016/S0264-3707(02)00013-3.

52. Data Users' Manual for the Advanced Microwave Scanning Radiometer 2
(AMSR2) Onboard the Global Change Observation Mission 1st - Water
``SHIZUKU'' (GCOM-W1) (Version 4.0) 2016.
